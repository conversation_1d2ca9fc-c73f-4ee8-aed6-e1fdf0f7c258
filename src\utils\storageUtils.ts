import { supabase } from '@/integrations/supabase/client'

export const checkAndCreateStorageBucket = async () => {
  try {
    // Check if the bucket exists by trying to list files
    const { data, error } = await supabase.storage
      .from('employee-documents')
      .list('', { limit: 1 })

    if (error && error.message.includes('bucket not found')) {
      console.log('Storage bucket not found, attempting to create...')
      
      // Try to create the bucket via SQL
      const { error: createError } = await supabase.rpc('create_storage_bucket_if_not_exists')
      
      if (createError) {
        console.error('Failed to create storage bucket:', createError)
        return false
      }
      
      console.log('Storage bucket created successfully')
      return true
    }
    
    if (error) {
      console.error('Error checking storage bucket:', error)
      return false
    }
    
    console.log('Storage bucket exists and is accessible')
    return true
  } catch (error) {
    console.error('Error in storage bucket check:', error)
    return false
  }
}

export const ensureStorageBucketExists = async () => {
  const exists = await checkAndCreateStorageBucket()
  if (!exists) {
    throw new Error('Storage bucket is not available. Please contact administrator.')
  }
  return true
}
