
import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { supabase } from "@/integrations/supabase/client"

interface SystemAccessData {
  role_id: string
  is_active: boolean
}

interface SystemAccessStepProps {
  data: SystemAccessData
  onChange: (data: Partial<SystemAccessData>) => void
}

interface Role {
  id: string
  name: string
  description: string
}

export default function SystemAccessStep({ data, onChange }: SystemAccessStepProps) {
  const [roles, setRoles] = useState<Role[]>([])
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)

  useEffect(() => {
    fetchRoles()
  }, [])

  useEffect(() => {
    if (data.role_id) {
      const role = roles.find(r => r.id === data.role_id)
      setSelectedRole(role || null)
    }
  }, [data.role_id, roles])

  const fetchRoles = async () => {
    try {
      const { data: rolesData, error } = await supabase
        .from('roles')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('name')

      if (error) throw error
      setRoles(rolesData || [])
    } catch (error) {
      console.error('Error fetching roles:', error)
    }
  }

  const handleInputChange = (field: keyof SystemAccessData, value: any) => {
    onChange({ [field]: value })
  }

  const getRoleIcon = (roleName: string) => {
    switch (roleName.toLowerCase()) {
      case 'admin':
        return '👑'
      case 'hr manager':
        return '👥'
      case 'manager':
        return '📊'
      case 'employee':
        return '👤'
      default:
        return '🔑'
    }
  }

  const getRoleColor = (roleName: string) => {
    switch (roleName.toLowerCase()) {
      case 'admin':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'hr manager':
        return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'manager':
        return 'text-purple-600 bg-purple-50 border-purple-200'
      case 'employee':
        return 'text-green-600 bg-green-50 border-green-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  return (
    <div className="space-y-6">
      {/* Role Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Role Assignment</h3>

        <div>
          <Label htmlFor="role" className="text-sm font-medium">
            System Role <span className="text-red-500">*</span>
          </Label>
          <Select 
            value={data.role_id} 
            onValueChange={(value) => handleInputChange('role_id', value)}
          >
            <SelectTrigger className="mt-1.5">
              <SelectValue placeholder="Select system role" />
            </SelectTrigger>
            <SelectContent>
              {roles.map((role) => (
                <SelectItem key={role.id} value={role.id}>
                  {role.name} {role.description && `- ${role.description}`}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {selectedRole && (
          <div className="bg-muted/30 rounded-lg p-4">
            <h4 className="font-medium mb-1">{selectedRole.name}</h4>
            <p className="text-sm text-gray-600">
              {selectedRole.description || 'No description available'}
            </p>
          </div>
        )}
      </div>

      {/* Access Control */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Access Control</h3>

        <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
          <div className="flex-1">
            <Label htmlFor="is_active" className="font-medium cursor-pointer">
              System Access
            </Label>
            <p className="text-sm text-gray-600">
              Enable or disable the employee's access to the system
            </p>
          </div>
          <Switch
            id="is_active"
            checked={data.is_active}
            onCheckedChange={(checked) => handleInputChange('is_active', checked)}
          />
        </div>
      </div>
    </div>
  )
}