import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { ArrowLeft, Calendar, User, FileText, Briefcase, Loader2 } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"

interface Employee {
  id: string
  employee_code: string
  user_id: string
  user_profiles: {
    first_name: string
    last_name: string
  }
}

export default function AddContractGroup() {
  const navigate = useNavigate()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [employees, setEmployees] = useState<Employee[]>([])
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()
  
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    employee_id: "",
    status: "active"
  })

  useEffect(() => {
    fetchEmployees()
  }, [])

  const fetchEmployees = async () => {
    // The data fetching logic remains the same
    try {
      const { data, error } = await supabase
        .from('employees')
        .select('id, employee_code, user_id, user_profiles(first_name, last_name)')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('employee_code');

      if (error) throw error;
      setEmployees(data as Employee[]);
    } catch (error) {
      console.error('Error fetching employees:', error)
      toast({
        title: "Error",
        description: "Failed to fetch employees. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name || !formData.employee_id || !startDate) {
      toast({
        title: "Missing Information",
        description: "Please fill out all required fields.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true)
    try {
      const { error } = await supabase
        .from('contract_groups')
        .insert({
          name: formData.name,
          description: formData.description,
          employee_id: formData.employee_id,
          start_date: startDate.toISOString().split('T')[0],
          end_date: endDate ? endDate.toISOString().split('T')[0] : null,
          status: formData.status
        });

      if (error) throw error

      toast({
        title: "Success!",
        description: "The contract group has been created successfully.",
      })
      navigate('/contracts')
    } catch (error) {
      console.error('Error creating contract group:', error)
      toast({
        title: "Creation Failed",
        description: "There was a problem creating the contract group.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Simplified Form Section Component
  const FormSection = ({ title, children }: { title: string, children: React.ReactNode }) => (
    <div className="space-y-4 pt-6 border-t first:pt-0 first:border-t-0">
      <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {children}
      </div>
    </div>
  );

  return (
    <Layout>
      <div className="bg-gray-50 min-h-screen">
        <div className="mx-auto max-w-4xl px-4 py-10">
          
          {/* Header */}
          <div className="mb-8">
            <Button variant="ghost" onClick={() => navigate('/contracts')} className="mb-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Contracts
            </Button>
            <h1 className="text-3xl font-bold text-gray-900">Create Contract Group</h1>
            <p className="text-gray-600 mt-1">Group multiple contracts under a single entity for an employee.</p>
          </div>

          {/* Form Card */}
          <Card>
            <form onSubmit={handleSubmit}>
              <CardContent className="p-6 space-y-8">

                <FormSection title="Core Details">
                  <div className="md:col-span-2">
                    <Label htmlFor="name">Group Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="e.g., Senior Developer Agreement 2025"
                      required
                      className="mt-1"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="employee">Employee *</Label>
                    <Select value={formData.employee_id} onValueChange={(value) => setFormData({ ...formData, employee_id: value })} required>
                      <SelectTrigger id="employee" className="mt-1">
                        <SelectValue placeholder="Select an employee" />
                      </SelectTrigger>
                      <SelectContent>
                        {employees.map((employee) => (
                          <SelectItem key={employee.id} value={employee.id}>
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-gray-500" />
                              <span>{`${employee.user_profiles.first_name} ${employee.user_profiles.last_name} (${employee.employee_code})`}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </FormSection>

                <FormSection title="Group Timeline">
                  <div>
                    <Label>Start Date *</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn("w-full justify-start text-left font-normal mt-1", !startDate && "text-muted-foreground")}
                        >
                          <Calendar className="mr-2 h-4 w-4" />
                          {startDate ? format(startDate, "PPP") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <CalendarComponent mode="single" selected={startDate} onSelect={setStartDate} initialFocus />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div>
                    <Label>End Date (Optional)</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn("w-full justify-start text-left font-normal mt-1", !endDate && "text-muted-foreground")}
                        >
                          <Calendar className="mr-2 h-4 w-4" />
                          {endDate ? format(endDate, "PPP") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <CalendarComponent mode="single" selected={endDate} onSelect={setEndDate} disabled={{ before: startDate }} initialFocus />
                      </PopoverContent>
                    </Popover>
                  </div>
                </FormSection>

                <FormSection title="Additional Information">
                   <div>
                    <Label htmlFor="status">Status</Label>
                    <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value })}>
                      <SelectTrigger id="status" className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="terminated">Terminated</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Add any notes or details about this contract group..."
                      className="mt-1"
                    />
                  </div>
                </FormSection>

              </CardContent>

              {/* Action Buttons in Card Footer */}
              <div className="flex items-center justify-end gap-4 p-6 bg-gray-50 border-t">
                <Button type="button" variant="ghost" onClick={() => navigate('/contracts')}>
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : "Create Contract Group"}
                </Button>
              </div>
            </form>
          </Card>
        </div>
      </div>
    </Layout>
  )
}