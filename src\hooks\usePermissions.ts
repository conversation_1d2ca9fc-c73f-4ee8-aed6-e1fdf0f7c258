import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/integrations/supabase/client'

interface Permission {
  id: string
  name: string
  module: string
  can_view: boolean
  can_add: boolean
  can_edit: boolean
  can_delete: boolean
}

export const usePermissions = () => {
  const { user, userRole } = useAuth()
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user && userRole) {
      fetchPermissions()
    }
  }, [user, userRole])

  const fetchPermissions = async () => {
    try {
      // Get role ID
      const { data: role } = await supabase
        .from('roles')
        .select('id')
        .eq('name', userRole)
        .single()

      if (!role) return

      // Get permissions for the role
      const { data: rolePermissions } = await supabase
        .from('role_permissions')
        .select(`
          permissions(
            id,
            name,
            module,
            can_view,
            can_add,
            can_edit,
            can_delete
          )
        `)
        .eq('role_id', role.id)
        .eq('is_active', true)

      const permissionsList = rolePermissions?.map(rp => rp.permissions).filter(Boolean) || []
      setPermissions(permissionsList)
    } catch (error) {
      console.error('Error fetching permissions:', error)
    } finally {
      setLoading(false)
    }
  }

  const hasPermission = (module: string, action: string = 'view') => {
    const permission = permissions.find(p => p.module === module)
    if (!permission) return false
    
    switch (action) {
      case 'view': return permission.can_view
      case 'add': return permission.can_add
      case 'edit': return permission.can_edit
      case 'delete': return permission.can_delete
      default: return false
    }
  }

  const canAccess = (module: string) => {
    return hasPermission(module, 'view')
  }

  return {
    permissions,
    loading,
    hasPermission,
    canAccess,
    userRole
  }
}