import { useState, useEffect } from "react"
import { Plus, Search, Edit, Trash2, Eye, MoreHorizontal, DollarSign, Calculator, Per<PERSON> } from "lucide-react"
import { Layout } from "@/components/Layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"

interface SalaryComponent {
  id: string
  name: string
  code: string
  component_type: 'earning' | 'deduction'
  calculation_type: 'fixed' | 'percentage' | 'formula'
  default_value?: number
  percentage_of?: string
  formula?: string
  description?: string
  is_taxable: boolean
  is_active: boolean
  created_at: string
}

export default function SalaryComponents() {
  const [components, setComponents] = useState<SalaryComponent[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)
  const [selectedComponent, setSelectedComponent] = useState<SalaryComponent | null>(null)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingComponent, setEditingComponent] = useState<SalaryComponent | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    component_type: "",
    calculation_type: "",
    default_value: "",
    percentage_of: "",
    formula: "",
    description: "",
    is_taxable: true,
    is_active: true
  })
  const { toast } = useToast()

  useEffect(() => {
    fetchComponents()
  }, [])

  const fetchComponents = async () => {
    try {
      const { data, error } = await supabase
        .from('salary_components')
        .select('*')
        .eq('is_deleted', false)
        .order('created_at', { ascending: false })

      if (error) throw error
      setComponents(data || [])
    } catch (error) {
      console.error('Error fetching salary components:', error)
      toast({
        title: "Error",
        description: "Failed to fetch salary components",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const filteredComponents = components.filter(component =>
    component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    component.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    component.component_type.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleView = (component: SalaryComponent) => {
    setSelectedComponent(component)
    setViewDialogOpen(true)
  }

  const handleEdit = (component: SalaryComponent) => {
    setEditingComponent(component)
    setFormData({
      name: component.name,
      code: component.code,
      component_type: component.component_type,
      calculation_type: component.calculation_type,
      default_value: component.default_value?.toString() || "",
      percentage_of: component.percentage_of || "",
      formula: component.formula || "",
      description: component.description || "",
      is_taxable: component.is_taxable,
      is_active: component.is_active
    })
    setIsDialogOpen(true)
  }

  const handleDelete = async (componentId: string) => {
    if (confirm('Are you sure you want to delete this salary component?')) {
      try {
        const { error } = await supabase
          .from('salary_components')
          .update({ is_deleted: true })
          .eq('id', componentId)
        
        if (error) throw error
        
        toast({
          title: "Success",
          description: "Salary component deleted successfully",
        })
        fetchComponents()
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete salary component",
          variant: "destructive",
        })
      }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name || !formData.code || !formData.component_type || !formData.calculation_type) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    try {
      if (editingComponent) {
        const { error } = await supabase
          .from('salary_components')
          .update({
            name: formData.name,
            code: formData.code.toUpperCase(),
            component_type: formData.component_type,
            calculation_type: formData.calculation_type,
            default_value: formData.default_value ? parseFloat(formData.default_value) : null,
            percentage_of: formData.percentage_of || null,
            formula: formData.formula || null,
            description: formData.description || null,
            is_taxable: formData.is_taxable,
            is_active: formData.is_active
          })
          .eq('id', editingComponent.id)

        if (error) throw error

        toast({
          title: "Success",
          description: "Salary component updated successfully",
        })
      } else {
        const { error } = await supabase
          .from('salary_components')
          .insert([{
            name: formData.name,
            code: formData.code.toUpperCase(),
            component_type: formData.component_type,
            calculation_type: formData.calculation_type,
            default_value: formData.default_value ? parseFloat(formData.default_value) : null,
            percentage_of: formData.percentage_of || null,
            formula: formData.formula || null,
            description: formData.description || null,
            is_taxable: formData.is_taxable,
            is_active: formData.is_active
          }])

        if (error) throw error

        toast({
          title: "Success",
          description: "Salary component created successfully",
        })
      }

      resetForm()
      setIsDialogOpen(false)
      setEditingComponent(null)
      fetchComponents()
    } catch (error: any) {
      console.error('Error saving salary component:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to save salary component",
        variant: "destructive",
      })
    }
  }

  const resetForm = () => {
    setFormData({
      name: "",
      code: "",
      component_type: "",
      calculation_type: "",
      default_value: "",
      percentage_of: "",
      formula: "",
      description: "",
      is_taxable: true,
      is_active: true
    })
  }

  const handleToggleStatus = async (componentId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('salary_components')
        .update({ is_active: !currentStatus })
        .eq('id', componentId)
      
      if (error) throw error
      
      toast({
        title: "Success",
        description: `Salary component ${!currentStatus ? 'activated' : 'deactivated'} successfully`,
      })
      fetchComponents()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update salary component status",
        variant: "destructive",
      })
    }
  }

  const getTypeIcon = (component_type: string) => {
    switch (component_type) {
      case 'earning': return <DollarSign className="h-4 w-4 text-green-600" />
      case 'deduction': return <DollarSign className="h-4 w-4 text-red-600" />
      default: return <DollarSign className="h-4 w-4" />
    }
  }

  const getCalculationIcon = (calculationType: string) => {
    switch (calculationType) {
      case 'fixed': return <DollarSign className="h-4 w-4" />
      case 'percentage': return <Percent className="h-4 w-4" />
      case 'formula': return <Calculator className="h-4 w-4" />
      default: return <Calculator className="h-4 w-4" />
    }
  }

  const getTypeBadge = (component_type: string) => {
    return (
      <Badge variant={component_type === 'earning' ? 'default' : 'destructive'}>
        {component_type}
      </Badge>
    )
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading salary components...</div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6 p-4 md:p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Salary Components</h1>
            <p className="text-muted-foreground">Manage salary earnings and deductions</p>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => {
                setEditingComponent(null)
                resetForm()
              }}>
                <Plus className="h-4 w-4 mr-2" />
                Add Component
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {editingComponent ? "Edit Salary Component" : "Add Salary Component"}
                </DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="e.g., Basic Salary"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="code">Code *</Label>
                    <Input
                      id="code"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                      placeholder="e.g., BASIC"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="component_type">Component Type *</Label>
                    <Select value={formData.component_type} onValueChange={(value) => setFormData({ ...formData, component_type: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select component type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="earning">Earning</SelectItem>
                        <SelectItem value="deduction">Deduction</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="calculation_type">Calculation Type *</Label>
                    <Select value={formData.calculation_type} onValueChange={(value) => setFormData({ ...formData, calculation_type: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select calculation type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fixed">Fixed Amount</SelectItem>
                        <SelectItem value="percentage">Percentage</SelectItem>
                        <SelectItem value="formula">Formula Based</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {formData.calculation_type === 'fixed' && (
                    <div className="space-y-2">
                      <Label htmlFor="default_value">Default Value</Label>
                      <Input
                        id="default_value"
                        type="number"
                        step="0.01"
                        min="0"
                        value={formData.default_value}
                        onChange={(e) => setFormData({ ...formData, default_value: e.target.value })}
                        placeholder="0.00"
                      />
                    </div>
                  )}

                  {formData.calculation_type === 'percentage' && (
                    <div className="space-y-2">
                      <Label htmlFor="percentage_of">Percentage Of</Label>
                      <Input
                        id="percentage_of"
                        value={formData.percentage_of}
                        onChange={(e) => setFormData({ ...formData, percentage_of: e.target.value })}
                        placeholder="e.g., BASIC"
                      />
                    </div>
                  )}
                </div>

                {formData.calculation_type === 'formula' && (
                  <div className="space-y-2">
                    <Label htmlFor="formula">Formula</Label>
                    <Textarea
                      id="formula"
                      value={formData.formula}
                      onChange={(e) => setFormData({ ...formData, formula: e.target.value })}
                      placeholder="Enter calculation formula"
                      rows={3}
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Component description"
                    rows={2}
                  />
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="is_taxable">Taxable</Label>
                      <p className="text-sm text-muted-foreground">Subject to tax calculations</p>
                    </div>
                    <Switch
                      id="is_taxable"
                      checked={formData.is_taxable}
                      onCheckedChange={(checked) => setFormData({ ...formData, is_taxable: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="is_active">Active</Label>
                      <p className="text-sm text-muted-foreground">Currently active component</p>
                    </div>
                    <Switch
                      id="is_active"
                      checked={formData.is_active}
                      onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsDialogOpen(false)
                      resetForm()
                    }}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">
                    {editingComponent ? "Update" : "Create"}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Salary Components List</CardTitle>
            <div className="flex items-center space-x-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search components..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>
          </CardHeader>
          <CardContent className="p-0">
            {/* Mobile Card View */}
            <div className="block md:hidden">
              {filteredComponents.map((component) => (
                <div key={component.id} className="border-b p-4 space-y-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2">
                        {getTypeIcon(component.component_type)}
                        <h3 className="font-medium">{component.name}</h3>
                      </div>
                      <p className="text-sm text-muted-foreground">Code: {component.code}</p>
                      <div className="flex gap-2 mt-2">
                        {getTypeBadge(component.component_type)}
                        <Badge variant="outline">{component.calculation_type}</Badge>
                        {component.is_taxable && <Badge variant="secondary">Taxable</Badge>}
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleView(component)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEdit(component)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDelete(component.id)} className="text-red-600">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={component.is_active}
                      onCheckedChange={() => handleToggleStatus(component.id, component.is_active)}
                    />
                    <span className="text-sm">{component.is_active ? "Active" : "Inactive"}</span>
                  </div>
                </div>
              ))}
              {filteredComponents.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No salary components found
                </div>
              )}
            </div>

            {/* Desktop Table View */}
            <div className="hidden md:block">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>Component Type</TableHead>
                    <TableHead>Calculation</TableHead>
                    <TableHead>Taxable</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredComponents.map((component) => (
                    <TableRow key={component.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          {getTypeIcon(component.component_type)}
                          {component.name}
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">{component.code}</TableCell>
                      <TableCell>{getTypeBadge(component.component_type)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getCalculationIcon(component.calculation_type)}
                          {component.calculation_type}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={component.is_taxable ? "default" : "secondary"}>
                          {component.is_taxable ? "Yes" : "No"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={component.is_active}
                            onCheckedChange={() => handleToggleStatus(component.id, component.is_active)}
                          />
                          <span className="text-sm">{component.is_active ? "Active" : "Inactive"}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm" onClick={() => handleView(component)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" onClick={() => handleEdit(component)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" onClick={() => handleDelete(component.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                  {filteredComponents.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        No salary components found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* View Component Dialog */}
        <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Salary Component Details</DialogTitle>
            </DialogHeader>
            {selectedComponent && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Name</label>
                    <p className="text-sm text-muted-foreground">{selectedComponent.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Code</label>
                    <p className="text-sm text-muted-foreground font-mono">{selectedComponent.code}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Component Type</label>
                    <div className="mt-1">{getTypeBadge(selectedComponent.component_type)}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Calculation Type</label>
                    <div className="flex items-center gap-2 mt-1">
                      {getCalculationIcon(selectedComponent.calculation_type)}
                      <span className="text-sm">{selectedComponent.calculation_type}</span>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Taxable</label>
                    <div className="mt-1">
                      <Badge variant={selectedComponent.is_taxable ? "default" : "secondary"}>
                        {selectedComponent.is_taxable ? "Yes" : "No"}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Status</label>
                    <div className="mt-1">
                      <Badge variant={selectedComponent.is_active ? "default" : "secondary"}>
                        {selectedComponent.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>
                  {selectedComponent.default_value && (
                    <div>
                      <label className="text-sm font-medium">Default Value</label>
                      <p className="text-sm text-muted-foreground">₹{selectedComponent.default_value.toLocaleString()}</p>
                    </div>
                  )}
                  {selectedComponent.percentage_of && (
                    <div>
                      <label className="text-sm font-medium">Percentage Of</label>
                      <p className="text-sm text-muted-foreground">{selectedComponent.percentage_of}</p>
                    </div>
                  )}
                </div>
                {selectedComponent.description && (
                  <div>
                    <label className="text-sm font-medium">Description</label>
                    <p className="text-sm text-muted-foreground">{selectedComponent.description}</p>
                  </div>
                )}
                {selectedComponent.formula && (
                  <div>
                    <label className="text-sm font-medium">Formula</label>
                    <p className="text-sm text-muted-foreground font-mono bg-gray-50 p-2 rounded">{selectedComponent.formula}</p>
                  </div>
                )}
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  )
}