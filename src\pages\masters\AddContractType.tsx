import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Plus, Trash2 } from "lucide-react";
import { Layout } from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface RequiredDocument {
  document_type: string;
  is_mandatory: boolean;
  remarks?: string;
}

interface ContractTemplate {
  name: string;
  content: string;
  variables?: Record<string, any>;
  version: number;
}

interface FormData {
  name: string;
  code: string;
  description: string;
  is_active: boolean;
}

export default function AddContractType() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState<FormData>({
    name: "",
    code: "",
    description: "",
    is_active: true,
  });

  const [requiredDocuments, setRequiredDocuments] = useState<RequiredDocument[]>([
    { document_type: "", is_mandatory: true, remarks: "" },
  ]);

  const [contractTemplates, setContractTemplates] = useState<ContractTemplate[]>([
    { name: "", content: "", version: 1 },
  ]);

  const addRequiredDocument = () => {
    setRequiredDocuments([...requiredDocuments, { document_type: "", is_mandatory: true, remarks: "" }]);
  };

  const removeRequiredDocument = (index: number) => {
    if (requiredDocuments.length === 1) {
      toast({
        title: "Warning",
        description: "At least one required document is needed.",
        variant: "destructive",
      });
      return;
    }
    setRequiredDocuments(requiredDocuments.filter((_, i) => i !== index));
  };

  const updateRequiredDocument = (
    index: number,
    field: keyof RequiredDocument,
    value: string | boolean
  ) => {
    const updated = [...requiredDocuments];
    updated[index] = { ...updated[index], [field]: value };
    setRequiredDocuments(updated);
  };

  const addContractTemplate = () => {
    setContractTemplates([...contractTemplates, { name: "", content: "", version: 1 }]);
  };

  const removeContractTemplate = (index: number) => {
    if (contractTemplates.length === 1) {
      toast({
        title: "Warning",
        description: "At least one contract template is needed.",
        variant: "destructive",
      });
      return;
    }
    setContractTemplates(contractTemplates.filter((_, i) => i !== index));
  };

  const updateContractTemplate = (
    index: number,
    field: keyof ContractTemplate,
    value: string | number
  ) => {
    const updated = [...contractTemplates];
    updated[index] = { ...updated[index], [field]: value };
    setContractTemplates(updated);
  };

  const validateForm = () => {
    if (!formData.name.trim() || !formData.code.trim() || !formData.description.trim()) {
      return "Please fill in all required fields (Name, Code, Description).";
    }
    if (formData.code.length > 20) {
      return "Code must be 20 characters or less.";
    }
    if (requiredDocuments.some(doc => !doc.document_type.trim())) {
      return "All required documents must have a document type.";
    }
    if (contractTemplates.some(template => !template.name.trim() || !template.content.trim())) {
      return "All contract templates must have a name and content.";
    }
    if (contractTemplates.some(template => template.version <= 0)) {
      return "Template version must be a positive number.";
    }
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    const validationError = validateForm();
    if (validationError) {
      toast({
        title: "Validation Error",
        description: validationError,
        variant: "destructive",
      });
      setLoading(false);
      return;
    }

    try {
      // Check for unique name and code
      const { data: existing, error: checkError } = await supabase
        .from("contract_types")
        .select("name, code")
        .or(`name.eq.${formData.name},code.eq.${formData.code}`);

      if (checkError) throw checkError;
      if (existing && existing.length > 0) {
        const errors: string[] = [];
        if (existing.some((item) => item.name === formData.name)) {
          errors.push("Contract type name already exists.");
        }
        if (existing.some((item) => item.code === formData.code)) {
          errors.push("Contract type code already exists.");
        }
        throw new Error(errors.join(" "));
      }

      // Insert contract type
      const { data: contractType, error: contractError } = await supabase
        .from("contract_types")
        .insert({
          name: formData.name,
          code: formData.code.toUpperCase(),
          description: formData.description,
          is_active: formData.is_active,
        })
        .select()
        .single();

      if (contractError) throw contractError;

      const contractTypeId = contractType.id;

      // Insert required documents
      const validDocuments = requiredDocuments.filter((doc) => doc.document_type.trim());
      if (validDocuments.length > 0) {
        const documentsToInsert = validDocuments.map((doc) => ({
          contract_type_id: contractTypeId,
          document_type: doc.document_type,
          is_mandatory: doc.is_mandatory,
          remarks: doc.remarks || null,
        }));

        const { error: documentsError } = await supabase
          .from("contract_type_required_documents")
          .insert(documentsToInsert);

        if (documentsError) throw documentsError;
      }

      // Insert contract templates
      const validTemplates = contractTemplates.filter(
        (template) => template.name.trim() && template.content.trim()
      );
      if (validTemplates.length > 0) {
        const templatesToInsert = validTemplates.map((template) => ({
          contract_type_id: contractTypeId,
          name: template.name,
          content: template.content,
          version: template.version,
          variables: template.variables || null,
        }));

        const { error: templatesError } = await supabase
          .from("contract_templates")
          .insert(templatesToInsert);

        if (templatesError) throw templatesError;
      }

      toast({
        title: "Success",
        description: "Contract type created successfully",
      });

      navigate("/masters/contract-types");
    } catch (error: any) {
      console.error("Error creating contract type:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to create contract type",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => navigate("/masters/contract-types")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Contract Types
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Add Contract Type</h1>
            <p className="text-muted-foreground">Create a new contract type with documents and templates</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Contract Type Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Employment Contract"
                    required
                    maxLength={100}
                  />
                </div>
                <div>
                  <Label htmlFor="code">Code *</Label>
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, code: e.target.value.toUpperCase() }))
                    }
                    placeholder="e.g., EMP, FRLNC"
                    required
                    maxLength={20}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe the purpose and scope of this contract type"
                  rows={3}
                  required
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) =>
                    setFormData((prev) => ({ ...prev, is_active: checked }))
                  }
                />
                <Label htmlFor="is_active">Active</Label>
              </div>
            </CardContent>
          </Card>

          {/* Required Documents */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Required Documents</CardTitle>
                <Button type="button" variant="outline" size="sm" onClick={addRequiredDocument}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Document
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {requiredDocuments.map((doc, index) => (
                <div key={index} className="space-y-4 p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="flex-1">
                      <Label htmlFor={`document_type_${index}`}>Document Type *</Label>
                      <Input
                        id={`document_type_${index}`}
                        placeholder="e.g., ID Proof, Address Proof"
                        value={doc.document_type}
                        onChange={(e) => updateRequiredDocument(index, "document_type", e.target.value)}
                        maxLength={50}
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id={`mandatory_${index}`}
                        checked={doc.is_mandatory}
                        onCheckedChange={(checked) =>
                          updateRequiredDocument(index, "is_mandatory", checked)
                        }
                      />
                      <Label htmlFor={`mandatory_${index}`}>Mandatory</Label>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeRequiredDocument(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  <div>
                    <Label htmlFor={`remarks_${index}`}>Remarks</Label>
                    <Textarea
                      id={`remarks_${index}`}
                      placeholder="Optional remarks about this document"
                      value={doc.remarks || ""}
                      onChange={(e) => updateRequiredDocument(index, "remarks", e.target.value)}
                      rows={2}
                    />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Contract Templates */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Contract Templates</CardTitle>
                <Button type="button" variant="outline" size="sm" onClick={addContractTemplate}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Template
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {contractTemplates.map((template, index) => (
                <div key={index} className="space-y-4 p-4 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 mr-4">
                      <Label htmlFor={`template_name_${index}`}>Template Name *</Label>
                      <Input
                        id={`template_name_${index}`}
                        placeholder="e.g., Standard Employment Contract"
                        value={template.name}
                        onChange={(e) => updateContractTemplate(index, "name", e.target.value)}
                        maxLength={255}
                      />
                    </div>
                    <div className="w-24">
                      <Label htmlFor={`version_${index}`}>Version *</Label>
                      <Input
                        id={`version_${index}`}
                        type="number"
                        min="1"
                        value={template.version}
                        onChange={(e) =>
                          updateContractTemplate(index, "version", parseInt(e.target.value) || 1)
                        }
                      />
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeContractTemplate(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  <div>
                    <Label htmlFor={`content_${index}`}>Template Content *</Label>
                    <Textarea
                      id={`content_${index}`}
                      placeholder="Enter the contract template content here..."
                      value={template.content}
                      onChange={(e) => updateContractTemplate(index, "content", e.target.value)}
                      rows={6}
                    />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline" onClick={() => navigate("/masters/contract-types")}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "Creating..." : "Create Contract Type"}
            </Button>
          </div>
        </form>
      </div>
    </Layout>
  );
}