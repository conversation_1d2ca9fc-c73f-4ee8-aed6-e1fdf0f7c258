import { useState, useEffect } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { ArrowLeft, Calendar, User, FileText, DollarSign, Building, Clock, AlertCircle, Save, X, Settings, Users, Briefcase, CreditCard, Edit, Info } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { cn } from "@/lib/utils"
import { format, differenceInDays, addDays } from "date-fns"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"

interface Contract {
  id: string
  employee_id: string
  contract_group_id: string
  contract_type_id: string
  start_date: string
  end_date: string
  status: string
  basic_salary: number
  overtime_allowed: boolean
  overtime_rate: number
  probation_period: number
  notice_period: number
}

interface ContractConflict {
  type: 'gap' | 'overlap'
  message: string
  affectedContract?: Contract
  gapDays?: number
  overlapDays?: number
}

export default function ReviseContract() {
  const navigate = useNavigate()
  const { toast } = useToast()
  const { id } = useParams()
  
  const [loading, setLoading] = useState(false)
  const [originalContract, setOriginalContract] = useState<Contract | null>(null)
  const [groupContracts, setGroupContracts] = useState<Contract[]>([])
  const [conflicts, setConflicts] = useState<ContractConflict[]>([])
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [salaryComponents, setSalaryComponents] = useState<any[]>([])
  const [selectedSalaryComponents, setSelectedSalaryComponents] = useState<any[]>([])
  
  const [formData, setFormData] = useState({
    contract_type_id: "",
    contract_template_id: "",
    overtime_allowed: false,
    overtime_rate: "",
    probation_period: "",
    notice_period: "",
    basic_salary: 0
  })

  useEffect(() => {
    if (id) {
      fetchContractData()
    }
  }, [id])

  useEffect(() => {
    if (startDate || endDate) {
      detectConflicts()
    }
  }, [startDate, endDate, groupContracts])

  const fetchContractData = async () => {
    try {
      // Fetch original contract
      const { data: contract, error } = await supabase
        .from('contracts')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      
      setOriginalContract(contract)
      setStartDate(new Date(contract.start_date))
      setEndDate(contract.end_date ? new Date(contract.end_date) : undefined)
      
      setFormData({
        contract_type_id: contract.contract_type_id,
        contract_template_id: contract.contract_template_id || "",
        overtime_allowed: contract.overtime_allowed,
        overtime_rate: contract.overtime_rate?.toString() || "",
        probation_period: contract.probation_period?.toString() || "",
        notice_period: contract.notice_period?.toString() || "",
        basic_salary: contract.basic_salary
      })

      // Fetch salary components
      const { data: salaryComponentsData, error: componentsError } = await supabase
        .from('salary_components')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('component_type', { ascending: false })
        .order('name')
      
      if (componentsError) {
        console.error('Error fetching salary components:', componentsError)
      }
      
      console.log('Available salary components:', salaryComponentsData)
      setSalaryComponents(salaryComponentsData || [])

      // Fetch existing salary components for this contract
      const { data: existingSalaryComponents, error: salaryError } = await supabase
        .from('employee_salary_components')
        .select(`
          salary_component_id,
          value,
          salary_components(*)
        `)
        .eq('contract_id', id)
        .eq('employee_id', contract.employee_id)
        .eq('is_deleted', false)
      
      if (salaryError) {
        console.error('Error fetching salary components:', salaryError)
      }
      
      console.log('Contract ID:', id)
      console.log('Existing salary components raw:', existingSalaryComponents)
      
      let mappedSalaryComponents = existingSalaryComponents?.map(esc => {
        console.log('Mapping contract salary component:', esc.salary_components?.name, 'Value:', esc.value)
        return {
          salary_component_id: esc.salary_component_id,
          value: esc.value,
          component: esc.salary_components
        }
      }).filter(sc => sc.component) || []
      
      // If no salary components found for contract, try to get by employee
      if (mappedSalaryComponents.length === 0) {
        console.log('No salary components found for contract, trying employee:', contract.employee_id)
        const { data: employeeSalaryComponents } = await supabase
          .from('employee_salary_components')
          .select(`
            salary_component_id,
            value,
            salary_components(*)
          `)
          .eq('employee_id', contract.employee_id)
          .eq('is_active', true)
          .order('created_at', { ascending: false })
        
        console.log('Employee salary components:', employeeSalaryComponents)
        
        mappedSalaryComponents = employeeSalaryComponents?.map(esc => {
          console.log('Mapping employee salary component:', esc.salary_components?.name, 'Value:', esc.value)
          return {
            salary_component_id: esc.salary_component_id,
            value: esc.value,
            component: esc.salary_components
          }
        }).filter(sc => sc.component) || []
      }
      
      console.log('Final mapped salary components:', mappedSalaryComponents)
      setSelectedSalaryComponents(mappedSalaryComponents)

      // Fetch all contracts in the same group for conflict detection
      if (contract.contract_group_id) {
        const { data: contracts } = await supabase
          .from('contracts')
          .select('*')
          .eq('contract_group_id', contract.contract_group_id)
          .eq('is_active', true)
          .eq('is_deleted', false)
          .neq('id', id)
          .order('start_date')
        
        setGroupContracts(contracts || [])
      }
    } catch (error) {
      console.error('Error fetching contract:', error)
      toast({
        title: "Error",
        description: "Failed to fetch contract data",
        variant: "destructive",
      })
    }
  }

  const detectConflicts = () => {
    if (!startDate || !originalContract) return

    const newConflicts: ContractConflict[] = []
    const newStart = startDate
    const newEnd = endDate
    const originalStart = new Date(originalContract.start_date)
    const originalEnd = originalContract.end_date ? new Date(originalContract.end_date) : null

    // Check for conflicts with other contracts in the group
    groupContracts.forEach(contract => {
      const contractStart = new Date(contract.start_date)
      const contractEnd = contract.end_date ? new Date(contract.end_date) : null

      // Check for overlaps
      if (newEnd && contractEnd) {
        if (newStart <= contractEnd && newEnd >= contractStart) {
          const overlapDays = Math.min(
            differenceInDays(newEnd, contractStart),
            differenceInDays(contractEnd, newStart)
          ) + 1

          newConflicts.push({
            type: 'overlap',
            message: `Overlap detected with contract from ${format(contractStart, "MMM dd, yyyy")} to ${format(contractEnd, "MMM dd, yyyy")} (${overlapDays} days)`,
            affectedContract: contract,
            overlapDays
          })
        }
      }
    })

    // Check for gaps (only informational)
    const sortedContracts = [...groupContracts].sort((a, b) => 
      new Date(a.start_date).getTime() - new Date(b.start_date).getTime()
    )

    // Find adjacent contracts and check for gaps
    sortedContracts.forEach(contract => {
      const contractStart = new Date(contract.start_date)
      const contractEnd = contract.end_date ? new Date(contract.end_date) : null

      // Check gap before this contract
      if (newEnd && newEnd < contractStart) {
        const gapDays = differenceInDays(contractStart, newEnd) - 1
        if (gapDays > 0) {
          newConflicts.push({
            type: 'gap',
            message: `Gap of ${gapDays} days between revised contract end and next contract start (${format(addDays(newEnd, 1), "MMM dd, yyyy")} to ${format(addDays(contractStart, -1), "MMM dd, yyyy")})`,
            affectedContract: contract,
            gapDays
          })
        }
      }

      // Check gap after this contract
      if (contractEnd && newStart > contractEnd) {
        const gapDays = differenceInDays(newStart, contractEnd) - 1
        if (gapDays > 0) {
          newConflicts.push({
            type: 'gap',
            message: `Gap of ${gapDays} days between previous contract end and revised contract start (${format(addDays(contractEnd, 1), "MMM dd, yyyy")} to ${format(addDays(newStart, -1), "MMM dd, yyyy")})`,
            affectedContract: contract,
            gapDays
          })
        }
      }
    })

    setConflicts(newConflicts)
  }

  const calculateTotalSalary = () => {
    console.log('Calculating total salary with components:', selectedSalaryComponents)
    
    const earnings = selectedSalaryComponents
      .filter(sc => sc.component && sc.component.component_type === 'earning')
      .reduce((sum, sc) => {
        console.log('Adding earning:', sc.component.name, sc.value)
        return sum + Number(sc.value)
      }, 0)
    
    const deductions = selectedSalaryComponents
      .filter(sc => sc.component && sc.component.component_type === 'deduction')
      .reduce((sum, sc) => {
        console.log('Adding deduction:', sc.component.name, sc.value)
        return sum + Number(sc.value)
      }, 0)
    
    console.log('Total earnings:', earnings, 'Total deductions:', deductions)
    const total = earnings - deductions
    console.log('Final total:', total)
    return total
  }

  const validateRevision = () => {
    const errors: string[] = []
    
    if (!startDate) {
      errors.push("Start date is required")
    }

    if (selectedSalaryComponents.length === 0) {
      errors.push("Please select at least one salary component")
    }

    // Check for overlaps (blocking)
    const hasOverlaps = conflicts.some(c => c.type === 'overlap')
    if (hasOverlaps) {
      errors.push("Contract overlaps must be resolved before saving")
    }

    setValidationErrors(errors)
    return errors.length === 0
  }

  const handleEditConflictingContract = (contractId: string) => {
    navigate(`/contracts/revise/${contractId}`)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateRevision()) {
      toast({
        title: "Error",
        description: "Please fix validation errors before submitting",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    try {
      // Determine new contract status
      const newStatus = originalContract?.status === 'active' ? 'active' : 'draft'
      
      const totalSalary = calculateTotalSalary()
      
      // Create revised contract
      const revisedContractData = {
        employee_id: originalContract?.employee_id,
        contract_group_id: originalContract?.contract_group_id,
        contract_type_id: formData.contract_type_id,
        contract_template_id: formData.contract_template_id || null,
        start_date: startDate?.toISOString().split('T')[0],
        end_date: endDate ? endDate.toISOString().split('T')[0] : null,
        basic_salary: totalSalary,
        overtime_allowed: formData.overtime_allowed,
        overtime_rate: formData.overtime_rate ? parseFloat(formData.overtime_rate) : null,
        probation_period: formData.probation_period ? parseInt(formData.probation_period) : null,
        notice_period: formData.notice_period ? parseInt(formData.notice_period) : null,
        status: newStatus,
        // revised_from: originalContract?.id // Column not yet available
      }

      const { data: newContract, error } = await supabase
        .from('contracts')
        .insert(revisedContractData)
        .select()
        .single()

      if (error) throw error

      const contractId = newContract.id

      // Insert employee salary components
      if (selectedSalaryComponents.length > 0) {
        // Soft delete old salary components for the original contract only
        await supabase
          .from('employee_salary_components')
          .update({ is_deleted: true, is_active: false })
          .eq('employee_id', originalContract?.employee_id)
          .eq('contract_id', originalContract?.id)
          .eq('is_deleted', false)
        
        const salaryComponentInserts = selectedSalaryComponents.map(sc => ({
          employee_id: originalContract?.employee_id,
          salary_component_id: sc.salary_component_id,
          value: sc.value,
          effective_from: startDate?.toISOString().split('T')[0],
          effective_to: endDate ? endDate.toISOString().split('T')[0] : null,
          contract_id: contractId,
          is_active: true,
          is_deleted: false
        }))
        
        const { error: salaryComponentError } = await supabase
          .from('employee_salary_components')
          .insert(salaryComponentInserts)
        
        if (salaryComponentError) throw salaryComponentError
      }

      // If original was active, terminate it
      if (originalContract?.status === 'active') {
        await supabase
          .from('contracts')
          .update({ status: 'terminated' })
          .eq('id', originalContract.id)
      }

      toast({
        title: "Success",
        description: `Contract revised successfully. ${newStatus === 'active' ? 'New contract is now active.' : 'New contract saved as draft.'}`,
      })
      
      navigate('/contracts')
    } catch (error) {
      console.error('Error revising contract:', error)
      toast({
        title: "Error",
        description: "Failed to revise contract",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  if (!originalContract) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading contract...</div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        {/* Header */}
        <div className="bg-white/70 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-40">
          <div className="px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center gap-6">
              <Button 
                variant="ghost" 
                onClick={() => navigate('/contracts')}
                className="hover:bg-white/80 transition-all duration-200 p-2 rounded-xl"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div className="space-y-1">
                <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  Revise Contract
                </h1>
                <p className="text-gray-600">
                  Create a new version of the contract with updated terms
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="px-4 sm:px-6 lg:px-8 py-8">
          <div className="max-w-6xl mx-auto">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Original Contract Info */}
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-6">
                  <div className="flex items-start gap-3">
                    <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-800">Original Contract</h4>
                      <p className="text-sm text-blue-700 mt-1">
                        Period: {format(new Date(originalContract.start_date), "MMM dd, yyyy")} - 
                        {originalContract.end_date ? format(new Date(originalContract.end_date), "MMM dd, yyyy") : "Ongoing"} | 
                        Status: {originalContract.status} | 
                        Salary: ₹{originalContract.basic_salary?.toLocaleString()}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Validation Errors */}
              {validationErrors.length > 0 && (
                <Card className="bg-red-50 border-red-200">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-red-800">Validation Errors</h4>
                        <ul className="mt-2 text-sm text-red-700 space-y-1">
                          {validationErrors.map((error, index) => (
                            <li key={index}>• {error}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Conflicts */}
              {conflicts.length > 0 && (
                <div className="space-y-4">
                  {conflicts.map((conflict, index) => (
                    <Alert key={index} className={conflict.type === 'overlap' ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50'}>
                      <AlertCircle className={`h-4 w-4 ${conflict.type === 'overlap' ? 'text-red-600' : 'text-yellow-600'}`} />
                      <AlertDescription className={conflict.type === 'overlap' ? 'text-red-800' : 'text-yellow-800'}>
                        <div className="flex items-center justify-between">
                          <span>{conflict.message}</span>
                          {conflict.affectedContract && (
                            <Button
                              type="button"
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditConflictingContract(conflict.affectedContract!.id)}
                              className="ml-4"
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              Edit Contract
                            </Button>
                          )}
                        </div>
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              )}

              {/* Timeline Section */}
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                <CardHeader className="border-b border-gray-100 bg-gradient-to-r from-purple-50 to-pink-50">
                  <CardTitle className="flex items-center gap-3 text-xl font-semibold text-gray-900">
                    <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                      <Calendar className="h-5 w-5 text-purple-600" />
                    </div>
                    Revised Timeline
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8 space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold text-gray-700">Start Date *</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full h-12 justify-start text-left font-normal border-gray-200 hover:border-blue-500",
                              !startDate && "text-muted-foreground"
                            )}
                          >
                            <Calendar className="mr-3 h-4 w-4 text-purple-600" />
                            {startDate ? format(startDate, "PPP") : "Pick a start date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <CalendarComponent
                            mode="single"
                            selected={startDate}
                            onSelect={setStartDate}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-semibold text-gray-700">End Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full h-12 justify-start text-left font-normal border-gray-200 hover:border-blue-500",
                              !endDate && "text-muted-foreground"
                            )}
                          >
                            <Calendar className="mr-3 h-4 w-4 text-purple-600" />
                            {endDate ? format(endDate, "PPP") : "Pick an end date (optional)"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <CalendarComponent
                            mode="single"
                            selected={endDate}
                            onSelect={setEndDate}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Financial Details */}
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                <CardHeader className="border-b border-gray-100 bg-gradient-to-r from-green-50 to-emerald-50">
                  <CardTitle className="flex items-center gap-3 text-xl font-semibold text-gray-900">
                    <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                      <DollarSign className="h-5 w-5 text-green-600" />
                    </div>
                    Financial Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8 space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-semibold text-gray-700">
                        Salary Components *
                      </Label>
                      <div className="text-right">
                        <p className="text-sm text-gray-600">Total Salary</p>
                        <p className="text-2xl font-bold text-green-600">
                          ₹{calculateTotalSalary().toLocaleString()}
                        </p>
                      </div>
                    </div>
                    
                    <div className="max-h-80 overflow-y-auto space-y-3 border border-gray-200 rounded-lg p-4">
                      {salaryComponents.length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                          <DollarSign className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                          <p>No salary components available</p>
                        </div>
                      )}
                      {salaryComponents.map((component) => {
                        const selectedComponent = selectedSalaryComponents.find(sc => sc.salary_component_id === component.id)
                        return (
                          <div key={component.id} className="border border-gray-200 rounded-lg p-4 bg-gradient-to-r from-white to-gray-50">
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center gap-3">
                                <input
                                  type="checkbox"
                                  checked={!!selectedComponent}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSelectedSalaryComponents([...selectedSalaryComponents, {
                                        salary_component_id: component.id,
                                        value: component.default_value || 0,
                                        component
                                      }])
                                    } else {
                                      setSelectedSalaryComponents(selectedSalaryComponents.filter(sc => sc.salary_component_id !== component.id))
                                    }
                                  }}
                                  className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
                                />
                                <div>
                                  <p className="font-medium text-gray-900">{component.name}</p>
                                  <p className="text-sm text-gray-600">{component.code} • {component.calculation_type}</p>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                  component.component_type === 'earning' 
                                    ? 'bg-green-100 text-green-800' 
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {component.component_type}
                                </span>
                                {component.is_taxable && (
                                  <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                    Taxable
                                  </span>
                                )}
                              </div>
                            </div>
                            
                            {selectedComponent && (
                              <div className="mt-3 pt-3 border-t border-gray-200">
                                <div className="flex items-center gap-4">
                                  <Label className="text-sm font-medium text-gray-700 min-w-0 flex-shrink-0">
                                    Amount (₹)
                                  </Label>
                                  <Input
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    value={selectedComponent.value}
                                    onChange={(e) => {
                                      const updatedComponents = selectedSalaryComponents.map(sc => 
                                        sc.salary_component_id === component.id 
                                          ? {...sc, value: parseFloat(e.target.value) || 0}
                                          : sc
                                      )
                                      setSelectedSalaryComponents(updatedComponents)
                                    }}
                                    className="flex-1 h-10 border-gray-200 focus:border-green-500 focus:ring-green-500"
                                    placeholder="Enter amount"
                                  />
                                </div>
                                {component.description && (
                                  <p className="text-xs text-gray-500 mt-2">{component.description}</p>
                                )}
                              </div>
                            )}
                          </div>
                        )
                      })}
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-xl">
                    <Checkbox
                      id="overtime_allowed"
                      checked={formData.overtime_allowed}
                      onCheckedChange={(checked) => setFormData({ ...formData, overtime_allowed: checked as boolean })}
                    />
                    <Label htmlFor="overtime_allowed" className="font-medium text-gray-800">
                      Allow Overtime Work
                    </Label>
                  </div>

                  {formData.overtime_allowed && (
                    <div className="space-y-3 ml-4 p-4 bg-green-50 rounded-xl border-l-4 border-green-500">
                      <Label htmlFor="overtime_rate" className="text-sm font-semibold text-gray-700">
                        Overtime Rate (per hour)
                      </Label>
                      <Input
                        id="overtime_rate"
                        type="number"
                        value={formData.overtime_rate}
                        onChange={(e) => setFormData({ ...formData, overtime_rate: e.target.value })}
                        placeholder="Enter overtime rate"
                        className="h-12 border-gray-200 focus:border-green-500 focus:ring-green-500"
                      />
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                <CardContent className="p-8">
                  <div className="flex flex-col sm:flex-row gap-4 justify-end">
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => navigate('/contracts')}
                      className="px-8 py-3 h-12 border-gray-300 hover:bg-gray-50"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                    
                    <Button 
                      type="submit" 
                      disabled={loading || conflicts.some(c => c.type === 'overlap')}
                      className="px-8 py-3 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                          Revising Contract...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          Revise Contract
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </form>
          </div>
        </div>
      </div>
    </Layout>
  )
}