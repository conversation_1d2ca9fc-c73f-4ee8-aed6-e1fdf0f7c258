import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom"
import { ArrowLeft, User, Mail, Building, CreditCard, Calendar, Settings, FileText, Check } from "lucide-react"
import { Layout } from "@/components/Layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"

// Step Components
import PersonalInfoStep from "@/components/employee/PersonalInfoStep"
import AuthenticationStep from "@/components/employee/AuthenticationStep"
import WorkDetailsStep from "@/components/employee/WorkDetailsStep"
import BankingInfoStep from "@/components/employee/BankingInfoStep"
import WorkScheduleStep from "@/components/employee/WorkScheduleStep"
import SystemAccessStep from "@/components/employee/SystemAccessStep"

interface FormData {
  personalInfo: {
    first_name: string
    middle_name: string
    last_name: string
    personal_email: string
    phone: string
    date_of_birth: Date | null
    gender: string
    address: string
    city: string
    state: string
    pincode: string
  }
  authDetails: {
    company_email: string
    password: string
    employee_code: string
    hire_date: Date | null
    department_id: string
    designation_id: string
  }
  workDetails: {
    employment_status: string
    onboarding_status: string
  }
  bankingInfo: {
    bank_name: string
    account_holder_name: string
    account_number: string
    ifsc_code: string
    branch_name: string
    is_primary: boolean
  }
  workSchedule: {
    shift_id: string
    work_week_id: string
  }
  systemAccess: {
    role_id: string
    is_active: boolean
  }
}

const steps = [
  { id: 1, title: "Personal", icon: User },
  { id: 2, title: "Auth", icon: Mail },
  { id: 3, title: "Work", icon: Building },
  { id: 4, title: "Banking", icon: CreditCard },
  { id: 5, title: "Schedule", icon: Calendar },
  { id: 6, title: "Access", icon: Settings },
]

export default function EditEmployee() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [formData, setFormData] = useState<FormData>({
    personalInfo: {
      first_name: "",
      middle_name: "",
      last_name: "",
      personal_email: "",
      phone: "",
      date_of_birth: null,
      gender: "",
      address: "",
      city: "",
      state: "",
      pincode: ""
    },
    authDetails: {
      company_email: "",
      password: "",
      employee_code: "",
      hire_date: null,
      department_id: "",
      designation_id: ""
    },
    workDetails: {
      employment_status: "active",
      onboarding_status: "approved"
    },
    bankingInfo: {
      bank_name: "",
      account_holder_name: "",
      account_number: "",
      ifsc_code: "",
      branch_name: "",
      is_primary: true
    },
    workSchedule: {
      shift_id: "",
      work_week_id: ""
    },
    systemAccess: {
      role_id: "",
      is_active: true
    }
  })

  useEffect(() => {
    if (id) {
      fetchEmployee()
    }
  }, [id])

  const fetchEmployee = async () => {
    try {
      // Get employee with all related data
      const { data: employee, error } = await supabase
        .from('employees')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error

      // Get user profile
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', employee.user_id)
        .single()

      // Get bank details
      const { data: bankDetails } = await supabase
        .from('employee_bank_details')
        .select('*')
        .eq('employee_id', id)
        .eq('is_primary', true)
        .single()

      // Get employee shift
      const { data: employeeShift } = await supabase
        .from('employee_shifts')
        .select('shift_id, work_week_id')
        .eq('employee_id', id)
        .eq('is_active', true)
        .single()

      // Get user role
      const { data: userRole } = await supabase
        .from('user_roles')
        .select('role_id')
        .eq('user_id', employee.user_id)
        .eq('is_active', true)
        .single()

      // Populate form data
      setFormData({
        personalInfo: {
          first_name: profile?.first_name || "",
          middle_name: profile?.middle_name || "",
          last_name: profile?.last_name || "",
          personal_email: profile?.personal_email || "",
          phone: profile?.phone || "",
          date_of_birth: profile?.date_of_birth ? new Date(profile.date_of_birth) : null,
          gender: profile?.gender || "",
          address: profile?.address || "",
          city: profile?.city || "",
          state: profile?.state || "",
          pincode: profile?.pincode || ""
        },
        authDetails: {
          company_email: employee.company_email || "",
          password: "", // Don't populate password
          employee_code: employee.employee_code || "",
          hire_date: employee.hire_date ? new Date(employee.hire_date) : null,
          department_id: employee.department_id || "",
          designation_id: employee.designation_id || ""
        },
        workDetails: {
          employment_status: employee.employment_status || "active",
          onboarding_status: employee.onboarding_status || "approved"
        },
        bankingInfo: {
          bank_name: bankDetails?.bank_name || "",
          account_holder_name: bankDetails?.account_holder_name || "",
          account_number: bankDetails?.account_number || "",
          ifsc_code: bankDetails?.ifsc_code || "",
          branch_name: bankDetails?.branch_name || "",
          is_primary: bankDetails?.is_primary || true
        },
        workSchedule: {
          shift_id: employeeShift?.shift_id || "",
          work_week_id: employeeShift?.work_week_id || ""
        },
        systemAccess: {
          role_id: userRole?.role_id || "",
          is_active: employee.is_active || true
        }
      })

      // Mark all steps as completed for edit mode
      setCompletedSteps([1, 2, 3, 4, 5, 6])
    } catch (error) {
      console.error('Error fetching employee:', error)
      toast({
        title: "Error",
        description: "Failed to fetch employee details",
        variant: "destructive",
      })
      navigate('/employees')
    } finally {
      setLoading(false)
    }
  }

  const updateFormData = (section: keyof FormData, data: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: { ...prev[section], ...data }
    }))
  }

  const handleSubmit = async () => {
    setSaving(true)
    try {
      // Get employee data
      const { data: employee } = await supabase
        .from('employees')
        .select('user_id')
        .eq('id', id)
        .single()

      if (!employee) throw new Error('Employee not found')

      // Update user profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .update({
          first_name: formData.personalInfo.first_name,
          middle_name: formData.personalInfo.middle_name || null,
          last_name: formData.personalInfo.last_name,
          personal_email: formData.personalInfo.personal_email,
          phone: formData.personalInfo.phone || null,
          date_of_birth: formData.personalInfo.date_of_birth?.toISOString().split('T')[0] || null,
          gender: formData.personalInfo.gender || null,
          city: formData.personalInfo.city || null,
          state: formData.personalInfo.state || null,
          address: formData.personalInfo.address || null,
          pincode: formData.personalInfo.pincode || null
        })
        .eq('id', employee.user_id)

      if (profileError) throw profileError

      // Update employee record
      const { error: employeeError } = await supabase
        .from('employees')
        .update({
          company_email: formData.authDetails.company_email,
          department_id: formData.authDetails.department_id || null,
          designation_id: formData.authDetails.designation_id || null,
          hire_date: formData.authDetails.hire_date?.toISOString().split('T')[0],
          employment_status: formData.workDetails.employment_status,
          onboarding_status: formData.workDetails.onboarding_status,
          is_active: formData.systemAccess.is_active
        })
        .eq('id', id)

      if (employeeError) throw employeeError

      // Update bank details if provided
      if (formData.bankingInfo.bank_name && formData.bankingInfo.account_number) {
        const { error: bankError } = await supabase
          .from('employee_bank_details')
          .upsert({
            employee_id: id,
            bank_name: formData.bankingInfo.bank_name,
            account_holder_name: formData.bankingInfo.account_holder_name,
            account_number: formData.bankingInfo.account_number,
            ifsc_code: formData.bankingInfo.ifsc_code,
            branch_name: formData.bankingInfo.branch_name || null,
            is_primary: formData.bankingInfo.is_primary
          })

        if (bankError) throw bankError
      }

      // Update employee shift
      if (formData.workSchedule.shift_id && formData.workSchedule.work_week_id) {
        const { error: shiftError } = await supabase
          .from('employee_shifts')
          .upsert({
            employee_id: id,
            shift_id: formData.workSchedule.shift_id,
            work_week_id: formData.workSchedule.work_week_id,
            is_active: true
          })

        if (shiftError) throw shiftError
      }

      // Update user role
      if (formData.systemAccess.role_id) {
        const { error: roleError } = await supabase
          .from('user_roles')
          .update({ role_id: formData.systemAccess.role_id })
          .eq('user_id', employee.user_id)

        if (roleError) throw roleError
      }

      toast({
        title: "Success",
        description: "Employee updated successfully",
      })
      navigate('/employees')
    } catch (error: any) {
      console.error('Error updating employee:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to update employee",
        variant: "destructive"
      })
    } finally {
      setSaving(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <PersonalInfoStep data={formData.personalInfo} onChange={(data) => updateFormData('personalInfo', data)} />
      case 2:
        return <AuthenticationStep data={formData.authDetails} onChange={(data) => updateFormData('authDetails', data)} />
      case 3:
        return <WorkDetailsStep data={formData.workDetails} onChange={(data) => updateFormData('workDetails', data)} />
      case 4:
        return <BankingInfoStep data={formData.bankingInfo} onChange={(data) => updateFormData('bankingInfo', data)} />
      case 5:
        return <WorkScheduleStep data={formData.workSchedule} onChange={(data) => updateFormData('workSchedule', data)} />
      case 6:
        return <SystemAccessStep data={formData.systemAccess} onChange={(data) => updateFormData('systemAccess', data)} />
      default:
        return null
    }
  }

  const progress = (currentStep / 6) * 100

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading employee details...</div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        {/* Header */}
        <div className="bg-white/70 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-40">
          <div className="px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center gap-6">
              <Button variant="ghost" onClick={() => navigate('/employees')}>
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  Edit Employee
                </h1>
                <p className="text-gray-600">Update employee information</p>
              </div>
            </div>
          </div>
        </div>

        <div className="px-4 sm:px-6 lg:px-8 py-8">
          <div className="max-w-6xl mx-auto">
            {/* Progress Bar */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm font-medium text-gray-700">Step {currentStep} of 6</span>
                <span className="text-sm text-gray-500">{Math.round(progress)}% Complete</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>

            {/* Step Navigation */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 mb-8">
              {steps.map((step) => {
                const Icon = step.icon
                const isCompleted = completedSteps.includes(step.id)
                const isCurrent = currentStep === step.id
                
                return (
                  <div
                    key={step.id}
                    className={`p-3 rounded-lg border text-center cursor-pointer transition-all ${
                      isCurrent
                        ? 'bg-blue-50 border-blue-200 text-blue-700'
                        : isCompleted
                        ? 'bg-green-50 border-green-200 text-green-700'
                        : 'bg-white border-gray-200 text-gray-500 hover:bg-gray-50'
                    }`}
                    onClick={() => setCurrentStep(step.id)}
                  >
                    <div className="flex items-center justify-center mb-2">
                      {isCompleted ? (
                        <Check className="h-5 w-5" />
                      ) : (
                        <Icon className="h-5 w-5" />
                      )}
                    </div>
                    <p className="text-xs font-medium">{step.title}</p>
                  </div>
                )
              })}
            </div>

            {/* Step Content */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 p-8 mb-8">
              {renderStepContent()}
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
                disabled={currentStep === 1}
              >
                Previous
              </Button>
              
              <div className="flex gap-4">
                <Button
                  variant="outline"
                  onClick={() => navigate('/employees')}
                >
                  Cancel
                </Button>
                
                {currentStep < 6 ? (
                  <Button
                    onClick={() => setCurrentStep(Math.min(6, currentStep + 1))}
                  >
                    Next
                  </Button>
                ) : (
                  <Button
                    onClick={handleSubmit}
                    disabled={saving}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {saving ? "Updating..." : "Update Employee"}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}