import { useState, useEffect } from "react"
import { Plus, Calendar, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useAuth } from "@/contexts/AuthContext"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { format } from "date-fns"

export default function MyLeaves() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [leaves, setLeaves] = useState<any[]>([])
  const [leaveTypes, setLeaveTypes] = useState<any[]>([])
  const [leaveBalance, setLeaveBalance] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [formData, setFormData] = useState({
    leave_type_id: '',
    start_date: '',
    end_date: '',
    reason: '',
    half_day: false
  })

  useEffect(() => {
    if (user) {
      fetchLeaveData()
    }
  }, [user])

  const fetchLeaveData = async () => {
    try {
      // Get employee ID
      const { data: employee } = await supabase
        .from('employees')
        .select('id')
        .eq('user_id', user?.id)
        .single()

      if (!employee) return

      // Get leave types
      const { data: types } = await supabase
        .from('leave_types')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false)

      // Get employee leaves
      const { data: employeeLeaves } = await supabase
        .from('employee_leaves')
        .select(`
          *,
          leave_types(name, color)
        `)
        .eq('employee_id', employee.id)
        .order('created_at', { ascending: false })

      // Get leave balance (from contract leaves)
      const { data: balance } = await supabase
        .from('contract_leaves')
        .select(`
          *,
          leave_types(name),
          contracts(employee_id)
        `)
        .eq('contracts.employee_id', employee.id)

      setLeaveTypes(types || [])
      setLeaves(employeeLeaves || [])
      setLeaveBalance(balance || [])
    } catch (error) {
      console.error('Error fetching leave data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const { data: employee } = await supabase
        .from('employees')
        .select('id')
        .eq('user_id', user?.id)
        .single()

      if (!employee) throw new Error('Employee not found')

      const { error } = await supabase
        .from('employee_leaves')
        .insert({
          employee_id: employee.id,
          leave_type_id: formData.leave_type_id,
          start_date: formData.start_date,
          end_date: formData.end_date,
          reason: formData.reason,
          half_day: formData.half_day,
          status: 'pending'
        })

      if (error) throw error

      toast({
        title: "Leave Applied",
        description: "Your leave application has been submitted for approval",
      })

      setDialogOpen(false)
      setFormData({
        leave_type_id: '',
        start_date: '',
        end_date: '',
        reason: '',
        half_day: false
      })
      fetchLeaveData()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      })
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'rejected': return <XCircle className="h-4 w-4 text-red-600" />
      default: return <AlertCircle className="h-4 w-4 text-yellow-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'default'
      case 'rejected': return 'destructive'
      default: return 'secondary'
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading leaves...</div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">My Leaves</h1>
            <p className="text-gray-600">Manage your leave applications</p>
          </div>
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Apply Leave
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Apply for Leave</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label>Leave Type</Label>
                  <Select value={formData.leave_type_id} onValueChange={(value) => setFormData(prev => ({...prev, leave_type_id: value}))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select leave type" />
                    </SelectTrigger>
                    <SelectContent>
                      {leaveTypes.map((type) => (
                        <SelectItem key={type.id} value={type.id}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Start Date</Label>
                    <Input
                      type="date"
                      value={formData.start_date}
                      onChange={(e) => setFormData(prev => ({...prev, start_date: e.target.value}))}
                      required
                    />
                  </div>
                  <div>
                    <Label>End Date</Label>
                    <Input
                      type="date"
                      value={formData.end_date}
                      onChange={(e) => setFormData(prev => ({...prev, end_date: e.target.value}))}
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <Label>Reason</Label>
                  <Textarea
                    value={formData.reason}
                    onChange={(e) => setFormData(prev => ({...prev, reason: e.target.value}))}
                    placeholder="Reason for leave..."
                    required
                  />
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">Apply Leave</Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Leave Balance */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {leaveBalance.map((balance) => (
            <Card key={balance.id}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">{balance.leave_types.name}</p>
                    <p className="text-2xl font-bold">{balance.days_allowed}</p>
                    <p className="text-xs text-gray-500">Days Available</p>
                  </div>
                  <Calendar className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Leave Applications */}
        <Card>
          <CardHeader>
            <CardTitle>Leave Applications</CardTitle>
          </CardHeader>
          <CardContent>
            {leaves.length > 0 ? (
              <div className="space-y-4">
                {leaves.map((leave) => (
                  <div key={leave.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(leave.status)}
                        <h3 className="font-medium">{leave.leave_types.name}</h3>
                      </div>
                      <Badge variant={getStatusColor(leave.status)}>
                        {leave.status}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Duration:</span>
                        <p className="font-medium">
                          {format(new Date(leave.start_date), 'MMM dd')} - {format(new Date(leave.end_date), 'MMM dd, yyyy')}
                        </p>
                      </div>
                      <div>
                        <span className="text-gray-600">Applied:</span>
                        <p className="font-medium">{format(new Date(leave.created_at), 'MMM dd, yyyy')}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Days:</span>
                        <p className="font-medium">{leave.days_requested} days</p>
                      </div>
                    </div>
                    
                    {leave.reason && (
                      <div className="mt-2">
                        <span className="text-gray-600 text-sm">Reason:</span>
                        <p className="text-sm">{leave.reason}</p>
                      </div>
                    )}
                    
                    {leave.status === 'rejected' && leave.rejection_reason && (
                      <div className="mt-2 p-2 bg-red-50 rounded">
                        <span className="text-red-600 text-sm font-medium">Rejection Reason:</span>
                        <p className="text-red-600 text-sm">{leave.rejection_reason}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Leave Applications</h3>
                <p className="text-gray-600">You haven't applied for any leaves yet</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Layout>
  )
}