// Copy exact same form structure as AddContract.tsx
import { useState, useEffect } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { ArrowLeft, Calendar, User, FileText, DollarSign, Building, Clock, CheckSquare, AlertCircle, Save, X, Settings, Users, Briefcase, CreditCard, Plus, Minus } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Checkbox } from "@/components/ui/checkbox"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"

export default function EditContract() {
  const navigate = useNavigate()
  const { toast } = useToast()
  const { id } = useParams()
  
  const [loading, setLoading] = useState(false)
  const [contract, setContract] = useState<any>(null)
  const [employees, setEmployees] = useState<any[]>([])
  const [contractTypes, setContractTypes] = useState<any[]>([])
  const [contractGroups, setContractGroups] = useState<any[]>([])
  const [contractTemplates, setContractTemplates] = useState<any[]>([])
  const [holidays, setHolidays] = useState<any[]>([])
  const [leaveTypes, setLeaveTypes] = useState<any[]>([])
  const [requiredDocuments, setRequiredDocuments] = useState<any[]>([])
  const [salaryComponents, setSalaryComponents] = useState<any[]>([])
  const [selectedSalaryComponents, setSelectedSalaryComponents] = useState<any[]>([])
  const [selectedHolidays, setSelectedHolidays] = useState<string[]>([])
  const [selectedLeaves, setSelectedLeaves] = useState<any[]>([])
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  
  const [formData, setFormData] = useState({
    employee_id: "",
    contract_group_id: "",
    contract_type_id: "",
    contract_template_id: "",
    overtime_allowed: false,
    overtime_rate: "",
    probation_period: "",
    notice_period: "",
    status: "draft"
  })

  useEffect(() => {
    if (id) {
      fetchContractData()
    }
  }, [id])

  const fetchContractData = async () => {
    try {
      // Fetch contract
      const { data: contractData, error } = await supabase
        .from('contracts')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      
      setContract(contractData)
      setStartDate(new Date(contractData.start_date))
      setEndDate(contractData.end_date ? new Date(contractData.end_date) : undefined)
      
      setFormData({
        employee_id: contractData.employee_id,
        contract_group_id: contractData.contract_group_id || "",
        contract_type_id: contractData.contract_type_id,
        contract_template_id: contractData.contract_template_id || "",
        overtime_allowed: contractData.overtime_allowed,
        overtime_rate: contractData.overtime_rate?.toString() || "",
        probation_period: contractData.probation_period?.toString() || "",
        notice_period: contractData.notice_period?.toString() || "",
        status: contractData.status
      })

      // Fetch all required data (same as AddContract)
      const [employeesData, typesData, groupsData, salaryComponentsData, holidaysData, leaveTypesData] = await Promise.all([
        supabase.from('employees').select('id, employee_code, user_id').eq('is_active', true).eq('is_deleted', false).order('employee_code'),
        supabase.from('contract_types').select('*').eq('is_active', true).eq('is_deleted', false).order('name'),
        supabase.from('contract_groups').select('id, name, employee_id, start_date, end_date').eq('is_active', true).eq('is_deleted', false).order('name'),
        supabase.from('salary_components').select('*').eq('is_active', true).eq('is_deleted', false).order('component_type', { ascending: false }).order('name'),
        supabase.from('holidays').select('id, name, start_date, end_date, type').eq('is_active', true).eq('is_deleted', false).order('name'),
        supabase.from('leave_types').select('id, name, code, description').eq('is_active', true).eq('is_deleted', false).order('name')
      ])

      // Fetch user profiles for employees
      const employeeUserIds = employeesData.data?.map(emp => emp.user_id) || []
      const { data: profilesData } = await supabase
        .from('user_profiles')
        .select('id, first_name, last_name')
        .in('id', employeeUserIds)

      const employeesWithProfiles = employeesData.data?.map(emp => ({
        ...emp,
        user_profiles: profilesData?.find(p => p.id === emp.user_id) || { first_name: '', last_name: '' }
      })) || []

      setEmployees(employeesWithProfiles)
      setContractTypes(typesData.data || [])
      setContractGroups(groupsData.data || [])
      setSalaryComponents(salaryComponentsData.data || [])
      setHolidays(holidaysData.data || [])
      setLeaveTypes(leaveTypesData.data || [])

      // Fetch contract templates if contract type is selected
      if (contractData.contract_type_id) {
        const { data: templatesData } = await supabase
          .from('contract_templates')
          .select('*')
          .eq('contract_type_id', contractData.contract_type_id)
          .eq('is_active', true)
          .eq('is_deleted', false)
          .order('name')
        setContractTemplates(templatesData || [])
      }

      // Fetch existing salary components
      const { data: existingSalaryComponents } = await supabase
        .from('employee_salary_components')
        .select(`
          salary_component_id,
          value,
          salary_components(*)
        `)
        .eq('contract_id', id)
        .eq('is_deleted', false)
      
      const mappedSalaryComponents = existingSalaryComponents?.map(esc => ({
        salary_component_id: esc.salary_component_id,
        value: esc.value,
        component: esc.salary_components
      })).filter(sc => sc.component) || []
      
      setSelectedSalaryComponents(mappedSalaryComponents)

      // Fetch existing holidays
      const { data: contractHolidays } = await supabase
        .from('contract_holidays')
        .select('holiday_id')
        .eq('contract_id', id)
        .eq('is_applicable', true)
      
      setSelectedHolidays(contractHolidays?.map(ch => ch.holiday_id) || [])

      // Fetch existing leaves
      const { data: contractLeaves } = await supabase
        .from('contract_leaves')
        .select('*')
        .eq('contract_id', id)
      
      setSelectedLeaves(contractLeaves || [])
    } catch (error) {
      console.error('Error fetching contract:', error)
      toast({
        title: "Error",
        description: "Failed to fetch contract data",
        variant: "destructive",
      })
    }
  }

  const calculateTotalSalary = () => {
    const earnings = selectedSalaryComponents
      .filter(sc => sc.component && sc.component.component_type === 'earning')
      .reduce((sum, sc) => sum + Number(sc.value), 0)
    
    const deductions = selectedSalaryComponents
      .filter(sc => sc.component && sc.component.component_type === 'deduction')
      .reduce((sum, sc) => sum + Number(sc.value), 0)
    
    return earnings - deductions
  }

  const handleContractTypeChange = async (contractTypeId: string) => {
    setFormData(prev => ({ ...prev, contract_type_id: contractTypeId }))
    
    // Fetch contract templates
    const { data: templatesData } = await supabase
      .from('contract_templates')
      .select('*')
      .eq('contract_type_id', contractTypeId)
      .eq('is_active', true)
      .eq('is_deleted', false)
      .order('name')
    
    setContractTemplates(templatesData || [])
    if (templatesData && templatesData.length > 0) {
      setFormData(prev => ({ ...prev, contract_template_id: templatesData[0].id }))
    }

    // Fetch required documents
    const { data: docsData } = await supabase
      .from('contract_type_required_documents')
      .select('*')
      .eq('contract_type_id', contractTypeId)
      .eq('is_active', true)
      .eq('is_deleted', false)
      .order('document_type')
    
    setRequiredDocuments(docsData || [])
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!startDate) {
      toast({ title: "Error", description: "Please select a start date", variant: "destructive" })
      return
    }

    if (selectedSalaryComponents.length === 0) {
      toast({ title: "Error", description: "Please select at least one salary component", variant: "destructive" })
      return
    }

    setLoading(true)
    try {
      const totalSalary = calculateTotalSalary()
      
      // Update contract
      const { error } = await supabase
        .from('contracts')
        .update({
          contract_type_id: formData.contract_type_id,
          contract_template_id: formData.contract_template_id || null,
          start_date: startDate.toISOString().split('T')[0],
          end_date: endDate ? endDate.toISOString().split('T')[0] : null,
          basic_salary: totalSalary,
          overtime_allowed: formData.overtime_allowed,
          overtime_rate: formData.overtime_rate ? parseFloat(formData.overtime_rate) : null,
          probation_period: formData.probation_period ? parseInt(formData.probation_period) : null,
          notice_period: formData.notice_period ? parseInt(formData.notice_period) : null,
          status: formData.status
        })
        .eq('id', id)

      if (error) throw error

      // Update salary components
      if (selectedSalaryComponents.length > 0) {
        // Soft delete existing components
        await supabase
          .from('employee_salary_components')
          .update({ is_deleted: true, is_active: false })
          .eq('contract_id', id)
          .eq('is_deleted', false)
        
        // Insert updated components
        const salaryComponentInserts = selectedSalaryComponents.map(sc => ({
          employee_id: contract.employee_id,
          salary_component_id: sc.salary_component_id,
          value: sc.value,
          effective_from: startDate.toISOString().split('T')[0],
          effective_to: endDate ? endDate.toISOString().split('T')[0] : null,
          contract_id: id,
          is_active: true,
          is_deleted: false
        }))
        
        const { error: salaryError } = await supabase
          .from('employee_salary_components')
          .insert(salaryComponentInserts)
        
        if (salaryError) throw salaryError
      }

      // Update contract holidays
      await supabase.from('contract_holidays').delete().eq('contract_id', id)
      if (selectedHolidays.length > 0) {
        const holidayInserts = selectedHolidays.map(holidayId => ({
          contract_id: id,
          holiday_id: holidayId,
          is_applicable: true
        }))
        await supabase.from('contract_holidays').insert(holidayInserts)
      }

      // Update contract leaves
      await supabase.from('contract_leaves').delete().eq('contract_id', id)
      if (selectedLeaves.length > 0) {
        const leaveInserts = selectedLeaves.map(leave => ({
          contract_id: id,
          leave_type_id: leave.leave_type_id,
          days_allowed: leave.days_allowed,
          carry_forward: leave.carry_forward,
          encashable: leave.encashable,
          salary_payable: leave.salary_payable
        }))
        await supabase.from('contract_leaves').insert(leaveInserts)
      }

      toast({
        title: "Success",
        description: "Contract updated successfully",
      })
      navigate('/contracts')
    } catch (error) {
      console.error('Error updating contract:', error)
      toast({
        title: "Error",
        description: "Failed to update contract",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  if (!contract) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading contract...</div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        {/* Header Section */}
        <div className="bg-white/70 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-40">
          <div className="px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center gap-6">
              <Button 
                variant="ghost" 
                onClick={() => navigate('/contracts')}
                className="hover:bg-white/80 transition-all duration-200 p-2 rounded-xl"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div className="space-y-1">
                <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  Edit Contract
                </h1>
                <p className="text-gray-600">
                  Update contract details and terms
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="px-4 sm:px-6 lg:px-8 py-8">
          <div className="max-w-6xl mx-auto">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Basic Information Section - Same as AddContract */}
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                <CardHeader className="border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
                  <CardTitle className="flex items-center gap-3 text-xl font-semibold text-gray-900">
                    <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                      <User className="h-5 w-5 text-blue-600" />
                    </div>
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="space-y-3">
                      <Label htmlFor="employee" className="text-sm font-semibold text-gray-700">
                        Employee *
                      </Label>
                      <div className="h-12 border border-gray-200 rounded-md px-3 py-2 bg-gray-50 flex items-center gap-3">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                          <User className="h-3 w-3 text-blue-600" />
                        </div>
                        <span className="font-medium">
                          {employees.find(emp => emp.id === formData.employee_id)?.user_profiles.first_name} {employees.find(emp => emp.id === formData.employee_id)?.user_profiles.last_name}
                          <span className="text-sm text-gray-500 ml-2">(locked)</span>
                        </span>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="contract_group" className="text-sm font-semibold text-gray-700">
                        Contract Group
                      </Label>
                      <Select value={formData.contract_group_id} disabled>
                        <SelectTrigger className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500">
                          <SelectValue placeholder={contractGroups.find(g => g.id === formData.contract_group_id)?.name || "No group selected"} />
                        </SelectTrigger>
                      </Select>
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="contract_type" className="text-sm font-semibold text-gray-700">
                        Contract Type *
                      </Label>
                      <Select value={formData.contract_type_id} onValueChange={handleContractTypeChange}>
                        <SelectTrigger className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500">
                          <SelectValue placeholder="Select contract type" />
                        </SelectTrigger>
                        <SelectContent>
                          {contractTypes.map((type) => (
                            <SelectItem key={type.id} value={type.id}>
                              <div className="flex items-center gap-3 py-1">
                                <Briefcase className="h-4 w-4 text-green-600" />
                                <div>
                                  <p className="font-medium">{type.name}</p>
                                  <p className="text-sm text-gray-500">{type.code}</p>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="contract_template" className="text-sm font-semibold text-gray-700">
                        Contract Template
                      </Label>
                      <Select 
                        value={formData.contract_template_id} 
                        onValueChange={(value) => setFormData({ ...formData, contract_template_id: value })}
                      >
                        <SelectTrigger className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500">
                          <SelectValue placeholder="Select template" />
                        </SelectTrigger>
                        <SelectContent>
                          {contractTemplates.map((template) => (
                            <SelectItem key={template.id} value={template.id}>
                              <div className="flex items-center gap-3 py-1">
                                <FileText className="h-4 w-4 text-orange-600" />
                                {template.name}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Timeline & Financial Section */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Timeline Section */}
                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                  <CardHeader className="border-b border-gray-100 bg-gradient-to-r from-purple-50 to-pink-50">
                    <CardTitle className="flex items-center gap-3 text-xl font-semibold text-gray-900">
                      <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                        <Calendar className="h-5 w-5 text-purple-600" />
                      </div>
                      Timeline
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-8 space-y-6">
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold text-gray-700">Start Date *</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full h-12 justify-start text-left font-normal border-gray-200 hover:border-blue-500",
                              !startDate && "text-muted-foreground"
                            )}
                          >
                            <Calendar className="mr-3 h-4 w-4 text-purple-600" />
                            {startDate ? format(startDate, "PPP") : "Pick a start date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <CalendarComponent
                            mode="single"
                            selected={startDate}
                            onSelect={setStartDate}
                            initialFocus
                            className="pointer-events-auto"
                          />
                        </PopoverContent>
                      </Popover>
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-semibold text-gray-700">End Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full h-12 justify-start text-left font-normal border-gray-200 hover:border-blue-500",
                              !endDate && "text-muted-foreground"
                            )}
                          >
                            <Calendar className="mr-3 h-4 w-4 text-purple-600" />
                            {endDate ? format(endDate, "PPP") : "Pick an end date (optional)"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <CalendarComponent
                            mode="single"
                            selected={endDate}
                            onSelect={setEndDate}
                            initialFocus
                            className="pointer-events-auto"
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </CardContent>
                </Card>

                {/* Financial Section */}
                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                  <CardHeader className="border-b border-gray-100 bg-gradient-to-r from-green-50 to-emerald-50">
                    <CardTitle className="flex items-center gap-3 text-xl font-semibold text-gray-900">
                      <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                        <CreditCard className="h-5 w-5 text-green-600" />
                      </div>
                      Financial Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-8 space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-semibold text-gray-700">
                          Salary Components *
                        </Label>
                        <div className="text-right">
                          <p className="text-sm text-gray-600">Total Salary</p>
                          <p className="text-2xl font-bold text-green-600">
                            ₹{calculateTotalSalary().toLocaleString()}
                          </p>
                        </div>
                      </div>
                      
                      <div className="max-h-80 overflow-y-auto space-y-3 border border-gray-200 rounded-lg p-4">
                        {salaryComponents.map((component) => {
                          const selectedComponent = selectedSalaryComponents.find(sc => sc.salary_component_id === component.id)
                          return (
                            <div key={component.id} className="border border-gray-200 rounded-lg p-4 bg-gradient-to-r from-white to-gray-50">
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center gap-3">
                                  <input
                                    type="checkbox"
                                    checked={!!selectedComponent}
                                    onChange={(e) => {
                                      if (e.target.checked) {
                                        setSelectedSalaryComponents([...selectedSalaryComponents, {
                                          salary_component_id: component.id,
                                          value: component.default_value || 0,
                                          component
                                        }])
                                      } else {
                                        setSelectedSalaryComponents(selectedSalaryComponents.filter(sc => sc.salary_component_id !== component.id))
                                      }
                                    }}
                                    className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
                                  />
                                  <div>
                                    <p className="font-medium text-gray-900">{component.name}</p>
                                    <p className="text-sm text-gray-600">{component.code} • {component.calculation_type}</p>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                    component.component_type === 'earning' 
                                      ? 'bg-green-100 text-green-800' 
                                      : 'bg-red-100 text-red-800'
                                  }`}>
                                    {component.component_type}
                                  </span>
                                  {component.is_taxable && (
                                    <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                      Taxable
                                    </span>
                                  )}
                                </div>
                              </div>
                              
                              {selectedComponent && (
                                <div className="mt-3 pt-3 border-t border-gray-200">
                                  <div className="flex items-center gap-4">
                                    <Label className="text-sm font-medium text-gray-700 min-w-0 flex-shrink-0">
                                      Amount (₹)
                                    </Label>
                                    <Input
                                      type="number"
                                      step="0.01"
                                      min="0"
                                      value={selectedComponent.value}
                                      onChange={(e) => {
                                        const updatedComponents = selectedSalaryComponents.map(sc => 
                                          sc.salary_component_id === component.id 
                                            ? {...sc, value: parseFloat(e.target.value) || 0}
                                            : sc
                                        )
                                        setSelectedSalaryComponents(updatedComponents)
                                      }}
                                      className="flex-1 h-10 border-gray-200 focus:border-green-500 focus:ring-green-500"
                                      placeholder="Enter amount"
                                    />
                                  </div>
                                  {component.description && (
                                    <p className="text-xs text-gray-500 mt-2">{component.description}</p>
                                  )}
                                </div>
                              )}
                            </div>
                          )
                        })}
                        
                        {salaryComponents.length === 0 && (
                          <div className="text-center py-8 text-gray-500">
                            <DollarSign className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                            <p>No salary components available</p>
                            <p className="text-sm">Please add salary components in the masters section first</p>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-xl">
                        <Checkbox
                          id="overtime_allowed"
                          checked={formData.overtime_allowed}
                          onCheckedChange={(checked) => setFormData({ ...formData, overtime_allowed: checked as boolean })}
                          className="data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                        />
                        <Label htmlFor="overtime_allowed" className="font-medium text-gray-800">
                          Allow Overtime Work
                        </Label>
                      </div>

                      {formData.overtime_allowed && (
                        <div className="space-y-3 ml-4 p-4 bg-green-50 rounded-xl border-l-4 border-green-500">
                          <Label htmlFor="overtime_rate" className="text-sm font-semibold text-gray-700">
                            Overtime Rate (per hour)
                          </Label>
                          <div className="relative">
                            <DollarSign className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-600" />
                            <Input
                              id="overtime_rate"
                              type="number"
                              value={formData.overtime_rate}
                              onChange={(e) => setFormData({ ...formData, overtime_rate: e.target.value })}
                              placeholder="Enter overtime rate"
                              className="pl-12 h-12 border-gray-200 focus:border-green-500 focus:ring-green-500"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Policy Details Section */}
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                <CardHeader className="border-b border-gray-100 bg-gradient-to-r from-orange-50 to-yellow-50">
                  <CardTitle className="flex items-center gap-3 text-xl font-semibold text-gray-900">
                    <div className="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center">
                      <Settings className="h-5 w-5 text-orange-600" />
                    </div>
                    Policy Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div className="space-y-3">
                      <Label htmlFor="probation_period" className="text-sm font-semibold text-gray-700">
                        Probation Period (months)
                      </Label>
                      <div className="relative">
                        <Clock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-orange-600" />
                        <Input
                          id="probation_period"
                          type="number"
                          value={formData.probation_period}
                          onChange={(e) => setFormData({ ...formData, probation_period: e.target.value })}
                          placeholder="Enter probation period"
                          className="pl-12 h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500"
                        />
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="notice_period" className="text-sm font-semibold text-gray-700">
                        Notice Period (days)
                      </Label>
                      <div className="relative">
                        <Calendar className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-orange-600" />
                        <Input
                          id="notice_period"
                          type="number"
                          value={formData.notice_period}
                          onChange={(e) => setFormData({ ...formData, notice_period: e.target.value })}
                          placeholder="Enter notice period"
                          className="pl-12 h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500"
                        />
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="status" className="text-sm font-semibold text-gray-700">
                        Status
                      </Label>
                      <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value })}>
                        <SelectTrigger className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="draft">
                            <div className="flex items-center gap-2">
                              <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                              Draft
                            </div>
                          </SelectItem>
                          <SelectItem value="active">
                            <div className="flex items-center gap-2">
                              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                              Active
                            </div>
                          </SelectItem>
                          <SelectItem value="terminated">
                            <div className="flex items-center gap-2">
                              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                              Terminated
                            </div>
                          </SelectItem>
                          <SelectItem value="expired">
                            <div className="flex items-center gap-2">
                              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                              Expired
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Holidays Section */}
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                <CardHeader className="border-b border-gray-100 bg-gradient-to-r from-teal-50 to-cyan-50">
                  <CardTitle className="flex items-center gap-3 text-xl font-semibold text-gray-900">
                    <div className="w-10 h-10 bg-teal-100 rounded-xl flex items-center justify-center">
                      <Calendar className="h-5 w-5 text-teal-600" />
                    </div>
                    Contract Holidays
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-80 overflow-y-auto">
                    {holidays.map((holiday) => (
                      <div key={holiday.id} className="flex items-center space-x-3 p-4 bg-gradient-to-r from-teal-50 to-cyan-50 rounded-xl border border-teal-100 hover:shadow-md transition-all duration-200">
                        <Checkbox
                          id={`holiday-${holiday.id}`}
                          checked={selectedHolidays.includes(holiday.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedHolidays([...selectedHolidays, holiday.id])
                            } else {
                              setSelectedHolidays(selectedHolidays.filter(id => id !== holiday.id))
                            }
                          }}
                          className="data-[state=checked]:bg-teal-600 data-[state=checked]:border-teal-600"
                        />
                        <Label htmlFor={`holiday-${holiday.id}`} className="text-sm font-medium cursor-pointer flex-1">
                          <div>
                            <p className="font-semibold text-gray-900">{holiday.name}</p>
                            <p className="text-xs text-gray-600 capitalize">{holiday.type}</p>
                          </div>
                        </Label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Leave Types Section */}
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                <CardHeader className="border-b border-gray-100 bg-gradient-to-r from-indigo-50 to-purple-50">
                  <CardTitle className="flex items-center gap-3 text-xl font-semibold text-gray-900">
                    <div className="w-10 h-10 bg-indigo-100 rounded-xl flex items-center justify-center">
                      <Users className="h-5 w-5 text-indigo-600" />
                    </div>
                    Leave Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="space-y-6">
                    {leaveTypes.map((leaveType) => {
                      const selectedLeave = selectedLeaves.find(l => l.leave_type_id === leaveType.id)
                      return (
                        <div key={leaveType.id} className="border border-gray-200 rounded-xl p-6 bg-gradient-to-r from-white to-gray-50 hover:shadow-md transition-all duration-200">
                          <div className="flex items-center space-x-3 mb-4">
                            <Checkbox
                              id={`leave-${leaveType.id}`}
                              checked={!!selectedLeave}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedLeaves([...selectedLeaves, {
                                    leave_type_id: leaveType.id,
                                    days_allowed: 0,
                                    carry_forward: false,
                                    encashable: false,
                                    salary_payable: true
                                  }])
                                } else {
                                  setSelectedLeaves(selectedLeaves.filter(l => l.leave_type_id !== leaveType.id))
                                }
                              }}
                              className="data-[state=checked]:bg-indigo-600 data-[state=checked]:border-indigo-600"
                            />
                            <Label htmlFor={`leave-${leaveType.id}`} className="font-semibold text-gray-900 cursor-pointer">
                              {leaveType.name} ({leaveType.code})
                            </Label>
                          </div>
                          
                          {selectedLeave && (
                            <div className="ml-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-4 p-4 bg-indigo-50 rounded-xl">
                              <div className="space-y-2">
                                <Label className="text-xs font-semibold text-gray-700">Days Allowed</Label>
                                <Input
                                  type="number"
                                  value={selectedLeave.days_allowed}
                                  onChange={(e) => {
                                    const updatedLeaves = selectedLeaves.map(l => 
                                      l.leave_type_id === leaveType.id 
                                        ? {...l, days_allowed: parseInt(e.target.value) || 0}
                                        : l
                                    )
                                    setSelectedLeaves(updatedLeaves)
                                  }}
                                  className="h-10 border-gray-200 focus:border-indigo-500 focus:ring-indigo-500"
                                  placeholder="0"
                                />
                              </div>
                              
                              <div className="flex items-center space-x-2 p-3 bg-white rounded-lg">
                                <Checkbox
                                  checked={selectedLeave.carry_forward}
                                  onCheckedChange={(checked) => {
                                    const updatedLeaves = selectedLeaves.map(l => 
                                      l.leave_type_id === leaveType.id 
                                        ? {...l, carry_forward: checked as boolean}
                                        : l
                                    )
                                    setSelectedLeaves(updatedLeaves)
                                  }}
                                  className="data-[state=checked]:bg-indigo-600 data-[state=checked]:border-indigo-600"
                                />
                                <Label className="text-xs font-medium cursor-pointer">Carry Forward</Label>
                              </div>
                              
                              <div className="flex items-center space-x-2 p-3 bg-white rounded-lg">
                                <Checkbox
                                  checked={selectedLeave.encashable}
                                  onCheckedChange={(checked) => {
                                    const updatedLeaves = selectedLeaves.map(l => 
                                      l.leave_type_id === leaveType.id 
                                        ? {...l, encashable: checked as boolean}
                                        : l
                                    )
                                    setSelectedLeaves(updatedLeaves)
                                  }}
                                  className="data-[state=checked]:bg-indigo-600 data-[state=checked]:border-indigo-600"
                                />
                                <Label className="text-xs font-medium cursor-pointer">Encashable</Label>
                              </div>
                              
                              <div className="flex items-center space-x-2 p-3 bg-white rounded-lg">
                                <Checkbox
                                  checked={selectedLeave.salary_payable}
                                  onCheckedChange={(checked) => {
                                    const updatedLeaves = selectedLeaves.map(l => 
                                      l.leave_type_id === leaveType.id 
                                        ? {...l, salary_payable: checked as boolean}
                                        : l
                                    )
                                    setSelectedLeaves(updatedLeaves)
                                  }}
                                  className="data-[state=checked]:bg-indigo-600 data-[state=checked]:border-indigo-600"
                                />
                                <Label className="text-xs font-medium cursor-pointer">Salary Payable</Label>
                              </div>
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                <CardContent className="p-8">
                  <div className="flex flex-col sm:flex-row gap-4 justify-end">
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => navigate('/contracts')}
                      className="px-8 py-3 h-12 border-gray-300 hover:bg-gray-50 transition-all duration-200"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                    
                    <Button 
                      type="submit" 
                      disabled={loading}
                      className="px-8 py-3 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                          Updating Contract...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          Update Contract
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </form>
          </div>
        </div>
      </div>
    </Layout>
  )
}