import { useState, useEffect } from "react"
import { <PERSON>, useNavigate } from "react-router-dom"
import { Plus, Search, Edit, Trash2, Eye, MoreHorizontal, Users, FileText, AlertCircle, Filter, Calendar, DollarSign, Clock, Building, GitBranch, User, Loader2, ArrowLeft } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectI<PERSON>, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { cn } from "@/lib/utils"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { format } from "date-fns"

// Interfaces (assuming they are defined as in the original code)
interface ContractGroup {
  id: string
  name: string
  description: string
  employee_id: string
  start_date: string
  end_date: string
  status: string
  is_active: boolean
  created_at: string
  employee?: {
    id: string
    employee_code: string
    user_id: string
    user_profiles?: {
      first_name: string
      last_name: string
    }
  }
  contracts: Contract[]
}

interface Contract {
  id: string
  employee_id: string
  contract_group_id: string
  contract_type_id: string
  start_date: string
  end_date: string
  basic_salary: number
  overtime_allowed: boolean
  overtime_rate: number
  probation_period: number
  notice_period: number
  status: string
  version: number
  is_active: boolean
  created_at: string
  employee?: {
    id: string
    employee_code: string
    user_id: string
    user_profiles?: {
      first_name: string
      last_name: string
    }
  }
  contract_types?: {
    name: string
    code: string
  }
}

interface Employee {
  id: string
  employee_code: string
  user_id: string
  user_profiles: {
    first_name: string
    last_name: string
  }
}

export default function Contracts() {
  const [contractGroups, setContractGroups] = useState<ContractGroup[]>([])
  const [contracts, setContracts] = useState<Contract[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)
  const [selectedContract, setSelectedContract] = useState<Contract | null>(null)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  const [view, setView] = useState<'groups' | 'contracts'>('groups')
  
  // AddContractGroup dialog states
  const [addGroupDialogOpen, setAddGroupDialogOpen] = useState(false)
  const [employees, setEmployees] = useState<Employee[]>([])
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()
  const [formLoading, setFormLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    employee_id: "",
    status: "active"
  })
  
  const { toast } = useToast()
  const navigate = useNavigate()

  useEffect(() => {
    fetchData()
    fetchEmployees()
  }, [])

  const fetchEmployees = async () => {
    try {
      const { data: employeesData, error } = await supabase
        .from('employees')
        .select('id, employee_code, user_id')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('employee_code');

      if (error) throw error;
      
      // Fetch user profiles separately
      const userIds = employeesData?.map(emp => emp.user_id) || [];
      const { data: profilesData } = await supabase
        .from('user_profiles')
        .select('id, first_name, last_name')
        .in('id', userIds);
      
      // Combine employees with profiles
      const employeesWithProfiles = employeesData?.map(emp => ({
        ...emp,
        user_profiles: profilesData?.find(p => p.id === emp.user_id) || {
          first_name: '',
          last_name: ''
        }
      })) || [];
      
      setEmployees(employeesWithProfiles as Employee[]);
    } catch (error) {
      console.error('Error fetching employees:', error)
      toast({
        title: "Error",
        description: "Failed to fetch employees. Please try again.",
        variant: "destructive",
      })
    }
  }

  const fetchData = async () => {
    setLoading(true)
    try {
      // Fetching logic remains the same
      const { data: groupsData, error: groupsError } = await supabase
        .from('contract_groups')
        .select('*, employees(id, employee_code, user_id)')
        .eq('is_deleted', false)
        .order('created_at', { ascending: false });
      if (groupsError) throw groupsError;

      const { data: contractsData, error: contractsError } = await supabase
        .from('contracts')
        .select('*, contract_types(name, code)')
        .eq('is_deleted', false)
        .order('created_at', { ascending: false });
      if (contractsError) throw contractsError;

      const allEmployeeIds = [
        ...(groupsData?.map(g => g.employee_id) || []),
        ...(contractsData?.map(c => c.employee_id) || [])
      ];
      const uniqueEmployeeIds = [...new Set(allEmployeeIds)];

      let employeesWithProfiles: any[] = [];
      if (uniqueEmployeeIds.length > 0) {
        const { data: employeesData } = await supabase
          .from('employees')
          .select('id, employee_code, user_id')
          .in('id', uniqueEmployeeIds);

        const userIds = employeesData?.map(emp => emp.user_id) || [];
        const { data: profilesData } = await supabase
          .from('user_profiles')
          .select('id, first_name, last_name')
          .in('id', userIds);

        employeesWithProfiles = employeesData?.map(emp => ({
          ...emp,
          user_profiles: profilesData?.find(p => p.id === emp.user_id) || { first_name: '', last_name: '' }
        })) || [];
      }

      const groupsWithContracts = groupsData?.map(group => {
        const employee = employeesWithProfiles.find(e => e.id === group.employee_id);
        return {
          ...group,
          employee: {
            id: employee?.id || '',
            employee_code: employee?.employee_code || '',
            user_id: employee?.user_id || '',
            user_profiles: employee?.user_profiles || { first_name: '', last_name: '' }
          },
          contracts: contractsData?.filter(contract => contract.contract_group_id === group.id) || []
        };
      }) || [];

      const contractsWithEmployees = contractsData?.map(contract => {
        const employee = employeesWithProfiles.find(e => e.id === contract.employee_id);
        return {
          ...contract,
          employee: {
            id: employee?.id || '',
            employee_code: employee?.employee_code || '',
            user_id: employee?.user_id || '',
            user_profiles: employee?.user_profiles || { first_name: '', last_name: '' }
          }
        };
      }) || [];

      setContractGroups(groupsWithContracts)
      setContracts(contractsWithEmployees)
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: "Error",
        description: "Failed to fetch contract data.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Filtering logic remains the same
  const filteredGroups = contractGroups.filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    `${group.employee?.user_profiles?.first_name} ${group.employee?.user_profiles?.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.employee?.employee_code?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredContracts = contracts.filter(contract =>
    `${contract.employee?.user_profiles?.first_name} ${contract.employee?.user_profiles?.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contract.employee?.employee_code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contract.contract_types?.name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handlers remain the same
  const handleView = (contract: Contract) => {
    setSelectedContract(contract)
    setViewDialogOpen(true)
  }

  const handleEdit = (contractId: string) => navigate(`/contracts/edit/${contractId}`);
  const handleRevise = (contractId: string) => navigate(`/contracts/revise/${contractId}`);
  const handleViewHistory = (contractId: string) => navigate(`/contracts/history/${contractId}`);

  const handleDelete = async (contractId: string) => {
    if (confirm('Are you sure you want to delete this contract? This action cannot be undone.')) {
      try {
        const { error } = await supabase.from('contracts').update({ is_deleted: true }).eq('id', contractId);
        if (error) throw error;
        toast({ title: "Success", description: "Contract has been deleted." });
        fetchData();
      } catch (error) {
        toast({ title: "Error", description: "Failed to delete the contract.", variant: "destructive" });
      }
    }
  }

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      employee_id: "",
      status: "active"
    })
    setStartDate(undefined)
    setEndDate(undefined)
  }

  const handleOpenAddDialog = () => {
    resetForm()
    setAddGroupDialogOpen(true)
  }

  const handleSubmitGroup = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name || !formData.employee_id || !startDate) {
      toast({
        title: "Missing Information",
        description: "Please fill out all required fields.",
        variant: "destructive",
      });
      return;
    }

    setFormLoading(true)
    try {
      const { error } = await supabase
        .from('contract_groups')
        .insert({
          name: formData.name,
          description: formData.description,
          employee_id: formData.employee_id,
          start_date: startDate.toISOString().split('T')[0],
          end_date: endDate ? endDate.toISOString().split('T')[0] : null,
          status: formData.status
        });

      if (error) throw error

      toast({
        title: "Success!",
        description: "The contract group has been created successfully.",
      })
      setAddGroupDialogOpen(false)
      fetchData()
    } catch (error) {
      console.error('Error creating contract group:', error)
      toast({
        title: "Creation Failed",
        description: "There was a problem creating the contract group.",
        variant: "destructive",
      })
    } finally {
      setFormLoading(false)
    }
  }
  
  // UI helper functions can be simplified
  const getStatusBadge = (status: string) => {
    const statusStyles: Record<string, string> = {
      active: "bg-green-100 text-green-800",
      terminated: "bg-red-100 text-red-800",
      expired: "bg-yellow-100 text-yellow-800",
      draft: "bg-gray-100 text-gray-800",
    };
    return <Badge className={`${statusStyles[status] || 'bg-gray-100 text-gray-800'} font-medium capitalize`}>{status}</Badge>;
  };

  // Other logic (canAddContract, detectContractIssues, stats) remains the same
  const canAddContract = (group: ContractGroup) => {
    if (!group.end_date) return true;
    const groupEndDate = new Date(group.end_date).getTime();
    const latestContractEnd = Math.max(0, ...group.contracts.map(c => new Date(c.end_date).getTime()));
    return latestContractEnd < groupEndDate;
  };

  const detectContractIssues = (contracts: Contract[]) => {
    if (contracts.length <= 1) return [];
    const issues: Array<{ type: 'gap' | 'overlap'; message: string; severity: 'warning' | 'error' }> = [];
    const sorted = [...contracts].sort((a, b) => new Date(a.start_date).getTime() - new Date(b.start_date).getTime());
    for (let i = 0; i < sorted.length - 1; i++) {
      const current = sorted[i];
      const next = sorted[i + 1];
      if (!current.end_date) continue;
      const diff = (new Date(next.start_date).getTime() - new Date(current.end_date).getTime()) / (1000 * 3600 * 24);
      if (diff > 1) issues.push({ type: 'gap', message: `Gap of ${diff - 1} days found`, severity: 'warning' });
      else if (diff < 0) issues.push({ type: 'overlap', message: `Overlap of ${Math.abs(diff)} days found`, severity: 'error' });
    }
    return issues;
  };

  const totalContracts = contracts.length;
  const activeContracts = contracts.filter(c => c.status === 'active').length;
  const totalGroups = contractGroups.length;
  const avgSalary = contracts.length > 0 ? contracts.reduce((sum, c) => sum + c.basic_salary, 0) / contracts.length : 0;

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-screen">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-blue-500 border-dashed rounded-full animate-spin"></div>
            <p className="mt-4 text-lg text-gray-600">Loading Contracts...</p>
          </div>
        </div>
      </Layout>
    );
  }

  // Page Header Component
  const PageHeader = () => (
    <div className="mb-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Contracts Management</h1>
          <p className="mt-1 text-gray-600">Manage all employee contracts and contract groups.</p>
        </div>
        <Dialog open={addGroupDialogOpen} onOpenChange={setAddGroupDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleOpenAddDialog}>
              <Plus className="h-4 w-4 mr-2" /> Add Contract Group
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create Contract Group</DialogTitle>
              <DialogDescription>
                Group multiple contracts under a single entity for an employee.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmitGroup} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <Label htmlFor="name">Group Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="e.g., Senior Developer Agreement 2025"
                    required
                    className="mt-1"
                  />
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="employee">Employee *</Label>
                  <Select value={formData.employee_id} onValueChange={(value) => setFormData({ ...formData, employee_id: value })} required>
                    <SelectTrigger id="employee" className="mt-1">
                      <SelectValue placeholder="Select an employee" />
                    </SelectTrigger>
                    <SelectContent>
                      {employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id}>
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-500" />
                            <span>{`${employee.user_profiles.first_name} ${employee.user_profiles.last_name} (${employee.employee_code})`}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Start Date *</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn("w-full justify-start text-left font-normal mt-1", !startDate && "text-muted-foreground")}
                      >
                        <Calendar className="mr-2 h-4 w-4" />
                        {startDate ? format(startDate, "PPP") : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <CalendarComponent mode="single" selected={startDate} onSelect={setStartDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
                <div>
                  <Label>End Date (Optional)</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn("w-full justify-start text-left font-normal mt-1", !endDate && "text-muted-foreground")}
                      >
                        <Calendar className="mr-2 h-4 w-4" />
                        {endDate ? format(endDate, "PPP") : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <CalendarComponent mode="single" selected={endDate} onSelect={setEndDate} disabled={{ before: startDate }} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value })}>
                    <SelectTrigger id="status" className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="terminated">Terminated</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Add any notes or details about this contract group..."
                    className="mt-1"
                  />
                </div>
              </div>
              <div className="flex items-center justify-end gap-4 pt-4">
                <Button type="button" variant="ghost" onClick={() => setAddGroupDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={formLoading}>
                  {formLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : "Create Contract Group"}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
  
  // Stats Cards Component
  const StatCard = ({ title, value, icon, color }) => (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-500">{title}</p>
            <p className={`text-2xl font-bold ${color}`}>{value}</p>
          </div>
          <div className={`p-3 rounded-full bg-opacity-20 ${color.replace('text', 'bg')}`}>{icon}</div>
        </div>
      </CardContent>
    </Card>
  );

  const StatsDisplay = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <StatCard title="Total Contracts" value={totalContracts} icon={<FileText className="h-6 w-6" />} color="text-blue-600" />
      <StatCard title="Active Contracts" value={activeContracts} icon={<Clock className="h-6 w-6" />} color="text-green-600" />
      <StatCard title="Contract Groups" value={totalGroups} icon={<Building className="h-6 w-6" />} color="text-purple-600" />
      <StatCard title="Avg. Salary" value={`₹${(avgSalary / 1000).toFixed(0)}K`} icon={<DollarSign className="h-6 w-6" />} color="text-orange-600" />
    </div>
  );

  return (
    <Layout>
      <div className="p-4 sm:p-6 lg:p-8">
        <PageHeader />
        <StatsDisplay />
        
        <Card>
          <CardHeader>
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div className="flex bg-gray-100 p-1 rounded-lg">
                <Button variant={view === 'groups' ? 'secondary' : 'ghost'} onClick={() => setView('groups')} className="flex-1 justify-center"><Users className="h-4 w-4 mr-2" />Groups</Button>
                <Button variant={view === 'contracts' ? 'secondary' : 'ghost'} onClick={() => setView('contracts')} className="flex-1 justify-center"><FileText className="h-4 w-4 mr-2" />All Contracts</Button>
              </div>
              <div className="relative w-full md:w-auto">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input placeholder="Search..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="pl-10" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {view === 'groups' ? (
              <ContractGroupsView 
                groups={filteredGroups} 
                onAddContract={canAddContract} 
                onDetectIssues={detectContractIssues} 
                getStatusBadge={getStatusBadge}
                onView={handleView}
                onEdit={handleEdit}
                onRevise={handleRevise}
                onHistory={handleViewHistory}
                onDelete={handleDelete}
              />
            ) : (
              <ContractsTableView 
                contracts={filteredContracts} 
                getStatusBadge={getStatusBadge} 
                onView={handleView} 
                onEdit={handleEdit} 
                onRevise={handleRevise} 
                onHistory={handleViewHistory} 
                onDelete={handleDelete} 
              />
            )}
          </CardContent>
        </Card>
      </div>
      
      {/* View Contract Dialog */}
      <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
        <DialogContent className="sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle>Contract Details</DialogTitle>
            <DialogDescription>
              {selectedContract?.contract_types?.name} for {`${selectedContract?.employee?.user_profiles?.first_name} ${selectedContract?.employee?.user_profiles?.last_name}`}
            </DialogDescription>
          </DialogHeader>
          {selectedContract && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <p className="text-right font-medium text-gray-500">Employee</p>
                <p className="col-span-3">{`${selectedContract.employee?.user_profiles?.first_name} ${selectedContract.employee?.user_profiles?.last_name}`}</p>
              </div>
               <div className="grid grid-cols-4 items-center gap-4">
                <p className="text-right font-medium text-gray-500">Dates</p>
                <p className="col-span-3">{`${format(new Date(selectedContract.start_date), 'MMM dd, yyyy')} to ${selectedContract.end_date ? format(new Date(selectedContract.end_date), 'MMM dd, yyyy') : 'Ongoing'}`}</p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <p className="text-right font-medium text-gray-500">Salary</p>
                <p className="col-span-3">₹{selectedContract.basic_salary.toLocaleString()} / month</p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <p className="text-right font-medium text-gray-500">Status</p>
                <div className="col-span-3">{getStatusBadge(selectedContract.status)}</div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Layout>
  )
}

// Sub-components for cleaner structure

const ContractGroupsView = ({ groups, onAddContract, onDetectIssues, getStatusBadge, onView, onEdit, onRevise, onHistory, onDelete }) => {
  const navigate = useNavigate();
  
  if (groups.length === 0) {
    return (
      <div className="text-center py-16">
        <Building className="mx-auto h-12 w-12 text-gray-300" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">No contract groups found.</h3>
        <p className="mt-1 text-sm text-gray-500">Get started by creating a new contract group.</p>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      {groups.map(group => (
        <Card key={group.id} className="overflow-hidden">
          <CardHeader className="bg-gray-50">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
              <div>
                <CardTitle className="text-lg">{group.name}</CardTitle>
                <p className="text-sm text-gray-600 flex items-center gap-2 mt-1">
                  <Users className="h-4 w-4" />
                  <span>{`${group.employee?.user_profiles?.first_name} ${group.employee?.user_profiles?.last_name}`}</span>
                  <span className="text-gray-400">•</span>
                  <span>{group.employee?.employee_code}</span>
                </p>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild><Button variant="ghost" size="icon" className="h-8 w-8"><MoreHorizontal className="h-4 w-4" /></Button></DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => navigate(`/contracts/groups/edit/${group.id}`)}><Edit className="mr-2 h-4 w-4" />Edit Group</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h4 className="font-semibold text-gray-700">Contracts ({group.contracts.length})</h4>
              {onAddContract(group) && (
                <Button variant="outline" size="sm" onClick={() => navigate(`/contracts/add?groupId=${group.id}`)}>
                  <Plus className="h-4 w-4 mr-2" /> Add Contract
                </Button>
              )}
            </div>
            
            {onDetectIssues(group.contracts).length > 0 && (
              <div className="mb-4 p-3 bg-yellow-50 text-yellow-800 rounded-lg text-sm flex items-start gap-2 border border-yellow-200">
                <AlertCircle className="h-5 w-5 mt-0.5" /> 
                <div>
                  <p className="font-semibold">Timeline Issues Detected</p>
                  <ul className="list-disc list-inside">
                    {onDetectIssues(group.contracts).map((issue, idx) => <li key={idx}>{issue.message}</li>)}
                  </ul>
                </div>
              </div>
            )}
            
            <div className="space-y-3">
              {group.contracts.length > 0 ? group.contracts.map(contract => (
                <div key={contract.id} className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 p-3 bg-white rounded-lg border hover:shadow-sm transition-shadow">
                  <div className="flex-1">
                    <div className="flex items-center gap-3">
                      <h5 className="font-semibold text-gray-800">{contract.contract_types?.name || 'Unknown Type'}</h5>
                      {getStatusBadge(contract.status)}
                    </div>
                    <div className="text-sm text-gray-500 mt-1 flex flex-wrap items-center gap-x-3">
                      <span>{format(new Date(contract.start_date), 'MMM dd, yyyy')} - {contract.end_date ? format(new Date(contract.end_date), 'MMM dd, yyyy') : 'Ongoing'}</span>
                      <span className="hidden sm:inline">•</span>
                      <span>Version {contract.version}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-1 self-end sm:self-center">
                    <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-600 hover:bg-gray-100" onClick={() => onView(contract)}><Eye className="h-4 w-4" /></Button>
                    <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-600 hover:bg-gray-100" onClick={() => onEdit(contract.id)}><Edit className="h-4 w-4" /></Button>
                    <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-600 hover:bg-gray-100" onClick={() => onRevise(contract.id)}><FileText className="h-4 w-4" /></Button>
                    <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-600 hover:bg-gray-100" onClick={() => onHistory(contract.id)}><GitBranch className="h-4 w-4" /></Button>
                    <Button variant="ghost" size="icon" className="h-8 w-8 text-red-500 hover:bg-red-50" onClick={() => onDelete(contract.id)}><Trash2 className="h-4 w-4" /></Button>
                  </div>
                </div>
              )) : (
                 <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg">
                    <p>No contracts in this group yet.</p>
                 </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

const ContractsTableView = ({ contracts, getStatusBadge, onView, onEdit, onRevise, onHistory, onDelete }) => {
  if (contracts.length === 0) {
    return (
      <div className="text-center py-16">
        <FileText className="mx-auto h-12 w-12 text-gray-300" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">No contracts found.</h3>
        <p className="mt-1 text-sm text-gray-500">Try adjusting your search or filter settings.</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Employee</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Dates</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {contracts.map(contract => (
            <TableRow key={contract.id}>
              <TableCell className="font-medium">{`${contract.employee?.user_profiles?.first_name} ${contract.employee?.user_profiles?.last_name}`}</TableCell>
              <TableCell>{contract.contract_types?.name}</TableCell>
              <TableCell>{`${format(new Date(contract.start_date), 'PP')} - ${contract.end_date ? format(new Date(contract.end_date), 'PP') : 'Present'}`}</TableCell>
              <TableCell>{getStatusBadge(contract.status)}</TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild><Button variant="ghost" size="icon" className="h-8 w-8"><MoreHorizontal className="h-4 w-4" /></Button></DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onView(contract)}><Eye className="mr-2 h-4 w-4" />View</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onEdit(contract.id)}><Edit className="mr-2 h-4 w-4" />Edit</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onRevise(contract.id)}><FileText className="mr-2 h-4 w-4" />Revise</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onHistory(contract.id)}><GitBranch className="mr-2 h-4 w-4" />History</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onDelete(contract.id)} className="text-red-600 focus:text-red-600"><Trash2 className="mr-2 h-4 w-4" />Delete</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};