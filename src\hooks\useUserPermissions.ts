import { useState, useEffect } from 'react'
import { supabase } from '@/integrations/supabase/client'

interface Permission {
  id: string
  name: string
  module: string
  can_view: boolean
  can_add: boolean
  can_edit: boolean
  can_delete: boolean
}

export function useUserPermissions() {
  const [userRole, setUserRole] = useState<string | null>(null)
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchUserPermissions()
  }, [])

  const fetchUserPermissions = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        setLoading(false)
        return
      }

      // Step 1: Get user role
      const { data: userRoleData, error: roleError } = await supabase
        .from('user_roles')
        .select('role_id')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .single()

      if (roleError || !userRoleData) {
        console.error('Error fetching user role:', roleError)
        setLoading(false)
        return
      }

      // Step 2: Get role name
      const { data: role, error: roleNameError } = await supabase
        .from('roles')
        .select('name')
        .eq('id', userRoleData.role_id)
        .single()

      if (roleNameError || !role) {
        console.error('Error fetching role name:', roleNameError)
        setLoading(false)
        return
      }

      setUserRole(role.name)

      // Admin: no need to fetch permissions
      if (role.name === 'Admin') {
        setLoading(false)
        return
      }

      // Step 3: Get permissions via join
      const { data: rolePermissions, error: rolePermError } = await supabase
        .from('role_permissions')
        .select('permissions(id, name, module, can_view, can_add, can_edit, can_delete)')
        .eq('role_id', userRoleData.role_id)
        .eq('is_active', true)
        .eq('is_deleted', false)

      if (rolePermError) {
        console.error('Error fetching role permissions:', rolePermError)
        setLoading(false)
        return
      }

      const flatPermissions: Permission[] = (rolePermissions || [])
        .map((rp: any) => rp.permissions)
        .filter((p: Permission | null) => !!p)

      setPermissions(flatPermissions)
    } catch (error) {
      console.error('Error in fetchUserPermissions:', error)
    } finally {
      setLoading(false)
    }
  }

  const hasPermission = (module: string, action: 'view' | 'add' | 'edit' | 'delete'): boolean => {
    if (userRole === 'Admin') return true

    const modulePermissions = permissions.filter(p => p.module === module)
    if (!modulePermissions.length) return false

    return modulePermissions.some(permission => {
      switch (action) {
        case 'view': return permission.can_view
        case 'add': return permission.can_add
        case 'edit': return permission.can_edit
        case 'delete': return permission.can_delete
        default: return false
      }
    })
  }

  return {
    userRole,
    permissions,
    loading,
    hasPermission,
    isAdmin: userRole === 'Admin',
    isHRManager: userRole === 'HR Manager',
    isEmployee: userRole === 'Employee',

    // Users
    canViewUsers: () => hasPermission('users', 'view'),
    canAddUsers: () => hasPermission('users', 'add'),
    canEditUsers: () => hasPermission('users', 'edit'),
    canDeleteUsers: () => hasPermission('users', 'delete'),

    // Employees
    canViewEmployees: () => hasPermission('employees', 'view'),
    canAddEmployees: () => hasPermission('employees', 'add'),
    canEditEmployees: () => hasPermission('employees', 'edit'),
    canDeleteEmployees: () => hasPermission('employees', 'delete'),

    // Other modules
    canViewDashboard: () => hasPermission('dashboard', 'view'),
    canViewReports: () => hasPermission('reports', 'view'),
    canManageRoles: () => hasPermission('roles', 'view'),
    canManagePermissions: () => hasPermission('permissions', 'view')
  }
}
