import { useState, useEffect } from "react"
import { FileText, Calendar, DollarSign, Plus, Minus, Building, Users, Settings, Save, X } from "lucide-react"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { supabase } from "@/integrations/supabase/client"

interface ContractGroup {
  id?: string
  name: string
  start_date: Date | null
  end_date: Date | null
  contracts: ContractData[]
}

interface ContractData {
  id?: string
  contract_type_id: string
  contract_template_id: string
  start_date: Date | null
  end_date: Date | null
  probation_period: number
  notice_period: number
  overtime_allowed: boolean
  overtime_rate: number
  status: string
  salary_components: Array<{
    salary_component_id: string
    value: number
    component: SalaryComponent
  }>
  selected_holidays: string[]
  selected_leaves: Array<{
    leave_type_id: string
    days_allowed: number
    carry_forward: boolean
    encashable: boolean
    salary_payable: boolean
  }>
}

interface ContractInfoData {
  create_contract: boolean
  contract_groups: ContractGroup[]
}

interface ContractCreationStepProps {
  data: ContractInfoData
  onChange: (data: Partial<ContractInfoData>) => void
}

interface ContractType {
  id: string
  name: string
  code: string
  description: string
}

interface ContractTemplate {
  id: string
  name: string
  content: string
  contract_type_id: string
}

interface SalaryComponent {
  id: string
  name: string
  code: string
  component_type: 'earning' | 'deduction'
  calculation_type: 'fixed' | 'percentage' | 'formula'
  default_value?: number
  is_taxable: boolean
}

interface Holiday {
  id: string
  name: string
  start_date: string
  end_date: string
  type: string
}

interface LeaveType {
  id: string
  name: string
  code: string
  description: string
}

export default function ContractCreationStep({ data, onChange }: ContractCreationStepProps) {
  const [contractTypes, setContractTypes] = useState<ContractType[]>([])
  const [contractTemplates, setContractTemplates] = useState<ContractTemplate[]>([])
  const [salaryComponents, setSalaryComponents] = useState<SalaryComponent[]>([])
  const [holidays, setHolidays] = useState<Holiday[]>([])
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([])
  const [showGroupForm, setShowGroupForm] = useState(false)
  const [showContractForm, setShowContractForm] = useState<{show: boolean, groupIndex: number}>({show: false, groupIndex: -1})
  const [editingContract, setEditingContract] = useState<{groupIndex: number, contractIndex: number} | null>(null)

  useEffect(() => {
    fetchContractTypes()
    fetchSalaryComponents()
    fetchHolidays()
    fetchLeaveTypes()
  }, [])

  useEffect(() => {
    fetchContractTemplates()
  }, [])

  const fetchContractTypes = async () => {
    try {
      const { data: typesData, error } = await supabase
        .from('contract_types')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('name')

      if (error) throw error
      setContractTypes(typesData || [])
    } catch (error) {
      console.error('Error fetching contract types:', error)
    }
  }

  const fetchContractTemplates = async () => {
    try {
      const { data: templatesData, error } = await supabase
        .from('contract_templates')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('name')

      if (error) throw error
      setContractTemplates(templatesData || [])
    } catch (error) {
      console.error('Error fetching contract templates:', error)
    }
  }

  const fetchSalaryComponents = async () => {
    try {
      const { data: componentsData, error } = await supabase
        .from('salary_components')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('component_type', { ascending: false })
        .order('name')

      if (error) throw error
      setSalaryComponents(componentsData || [])
    } catch (error) {
      console.error('Error fetching salary components:', error)
    }
  }

  const fetchHolidays = async () => {
    try {
      const { data: holidaysData, error } = await supabase
        .from('holidays')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('name')

      if (error) throw error
      setHolidays(holidaysData || [])
    } catch (error) {
      console.error('Error fetching holidays:', error)
    }
  }

  const fetchLeaveTypes = async () => {
    try {
      const { data: leaveTypesData, error } = await supabase
        .from('leave_types')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('name')

      if (error) throw error
      setLeaveTypes(leaveTypesData || [])
    } catch (error) {
      console.error('Error fetching leave types:', error)
    }
  }

  const handleInputChange = (field: keyof ContractInfoData, value: any) => {
    onChange({ [field]: value })
  }

  const addContractGroup = (groupData: {name: string, start_date: Date | null, end_date: Date | null}) => {
    const newGroup: ContractGroup = {
      name: groupData.name,
      start_date: groupData.start_date,
      end_date: groupData.end_date,
      contracts: []
    }
    const updatedGroups = [...data.contract_groups, newGroup]
    onChange({ contract_groups: updatedGroups })
    setShowGroupForm(false)
  }

  const addContractToGroup = (groupIndex: number, contractData: ContractData) => {
    const updatedGroups = [...data.contract_groups]
    updatedGroups[groupIndex].contracts.push(contractData)
    onChange({ contract_groups: updatedGroups })
    setShowContractForm({show: false, groupIndex: -1})
    setEditingContract(null)
  }

  const updateContract = (groupIndex: number, contractIndex: number, contractData: ContractData) => {
    const updatedGroups = [...data.contract_groups]
    updatedGroups[groupIndex].contracts[contractIndex] = contractData
    onChange({ contract_groups: updatedGroups })
    setShowContractForm({show: false, groupIndex: -1})
    setEditingContract(null)
  }

  const removeContract = (groupIndex: number, contractIndex: number) => {
    const updatedGroups = [...data.contract_groups]
    updatedGroups[groupIndex].contracts.splice(contractIndex, 1)
    onChange({ contract_groups: updatedGroups })
  }

  const calculateTotalSalary = (salaryComponents: any[]) => {
    const earnings = salaryComponents
      .filter(sc => sc.component.component_type === 'earning')
      .reduce((sum, sc) => sum + sc.value, 0)
    
    const deductions = salaryComponents
      .filter(sc => sc.component.component_type === 'deduction')
      .reduce((sum, sc) => sum + sc.value, 0)
    
    return earnings - deductions
  }

  if (!data.create_contract) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <h3 className="text-lg font-semibold mb-2">Contract Creation</h3>
          <p className="text-gray-600 mb-6">
            You can create an employment contract for this employee or skip this step and create it later.
          </p>
          
          <div className="flex items-center justify-center space-x-3 p-4 bg-muted/30 rounded-lg max-w-md mx-auto">
            <Checkbox
              id="create_contract"
              checked={data.create_contract}
              onCheckedChange={(checked) => handleInputChange('create_contract', checked)}
            />
            <Label htmlFor="create_contract" className="font-medium cursor-pointer">
              Create employment contract now
            </Label>
          </div>
        </div>
      </div>
    )
  }

  // Group Form Component
  const GroupForm = () => {
    const [groupData, setGroupData] = useState({
      name: `Employment Contract ${new Date().getFullYear()}`,
      start_date: null as Date | null,
      end_date: null as Date | null
    })

    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
          <h3 className="text-lg font-semibold mb-4">Add Contract Group</h3>
          
          <div className="space-y-4">
            <div>
              <Label>Group Name *</Label>
              <Input
                value={groupData.name}
                onChange={(e) => setGroupData(prev => ({...prev, name: e.target.value}))}
                placeholder="Employment Contract 2024"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Start Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start">
                      <Calendar className="mr-2 h-4 w-4" />
                      {groupData.start_date ? format(groupData.start_date, "PPP") : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={groupData.start_date || undefined}
                      onSelect={(date) => setGroupData(prev => ({...prev, start_date: date || null}))}
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <div>
                <Label>End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start">
                      <Calendar className="mr-2 h-4 w-4" />
                      {groupData.end_date ? format(groupData.end_date, "PPP") : "Optional"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={groupData.end_date || undefined}
                      onSelect={(date) => setGroupData(prev => ({...prev, end_date: date || null}))}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
          
          <div className="flex gap-3 mt-6">
            <Button variant="outline" onClick={() => setShowGroupForm(false)} className="flex-1">
              Cancel
            </Button>
            <Button 
              onClick={() => addContractGroup(groupData)} 
              disabled={!groupData.name || !groupData.start_date}
              className="flex-1"
            >
              Add Group
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Contract Groups */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Contract Groups</h3>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowGroupForm(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Contract Group
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleInputChange('create_contract', false)}
            >
              Skip Contract
            </Button>
          </div>
        </div>

        {/* Display Contract Groups */}
        {data.contract_groups.map((group, groupIndex) => (
          <div key={groupIndex} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
            <div className="flex items-center justify-between mb-3">
              <div>
                <h4 className="font-medium text-gray-900">{group.name}</h4>
                <p className="text-sm text-gray-600">
                  {group.start_date ? format(group.start_date, "PPP") : "No start date"} - 
                  {group.end_date ? format(group.end_date, "PPP") : "No end date"}
                </p>
              </div>
              <Button
                size="sm"
                onClick={() => setShowContractForm({show: true, groupIndex})}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Contract
              </Button>
            </div>
            
            {/* Display Contracts in Group */}
            {group.contracts.length > 0 && (
              <div className="space-y-2">
                {group.contracts.map((contract, contractIndex) => (
                  <div key={contractIndex} className="bg-white p-3 rounded border flex items-center justify-between">
                    <div>
                      <p className="font-medium">Contract {contractIndex + 1}</p>
                      <p className="text-sm text-gray-600">
                        Salary: ₹{calculateTotalSalary(contract.salary_components).toLocaleString()}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setEditingContract({groupIndex, contractIndex})
                          setShowContractForm({show: true, groupIndex})
                        }}
                      >
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => removeContract(groupIndex, contractIndex)}
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}

        {data.contract_groups.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p>No contract groups created yet</p>
            <p className="text-sm">Click "Add Contract Group" to get started</p>
          </div>
        )}
      </div>

      {/* Modals */}
      {showGroupForm && <GroupForm />}
      
      {showContractForm.show && (
        <ContractFormModal 
          groupIndex={showContractForm.groupIndex}
          contractData={editingContract ? data.contract_groups[editingContract.groupIndex].contracts[editingContract.contractIndex] : null}
          contractTypes={contractTypes}
          contractTemplates={contractTemplates}
          salaryComponents={salaryComponents}
          holidays={holidays}
          leaveTypes={leaveTypes}
          onSave={(contractData) => {
            if (editingContract) {
              updateContract(editingContract.groupIndex, editingContract.contractIndex, contractData)
            } else {
              addContractToGroup(showContractForm.groupIndex, contractData)
            }
          }}
          onCancel={() => {
            setShowContractForm({show: false, groupIndex: -1})
            setEditingContract(null)
          }}
        />
      )}
    </div>
  )
}

// Contract Form Modal Component
function ContractFormModal({ 
  groupIndex, 
  contractData, 
  contractTypes, 
  contractTemplates, 
  salaryComponents, 
  holidays, 
  leaveTypes, 
  onSave, 
  onCancel 
}: {
  groupIndex: number
  contractData: ContractData | null
  contractTypes: ContractType[]
  contractTemplates: ContractTemplate[]
  salaryComponents: SalaryComponent[]
  holidays: Holiday[]
  leaveTypes: LeaveType[]
  onSave: (data: ContractData) => void
  onCancel: () => void
}) {
  const [formData, setFormData] = useState<ContractData>(contractData || {
    contract_type_id: "",
    contract_template_id: "",
    start_date: null,
    end_date: null,
    probation_period: 6,
    notice_period: 30,
    overtime_allowed: false,
    overtime_rate: 0,
    status: "draft",
    salary_components: [],
    selected_holidays: [],
    selected_leaves: []
  })

  const [filteredTemplates, setFilteredTemplates] = useState<ContractTemplate[]>([])

  useEffect(() => {
    if (formData.contract_type_id) {
      setFilteredTemplates(contractTemplates.filter(t => t.contract_type_id === formData.contract_type_id))
    }
  }, [formData.contract_type_id, contractTemplates])

  const updateField = (field: keyof ContractData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const addSalaryComponent = (component: SalaryComponent) => {
    const newComponent = {
      salary_component_id: component.id,
      value: component.default_value || 0,
      component
    }
    updateField('salary_components', [...formData.salary_components, newComponent])
  }

  const removeSalaryComponent = (index: number) => {
    const updated = formData.salary_components.filter((_, i) => i !== index)
    updateField('salary_components', updated)
  }

  const updateSalaryComponentValue = (index: number, value: number) => {
    const updated = formData.salary_components.map((sc, i) => 
      i === index ? { ...sc, value } : sc
    )
    updateField('salary_components', updated)
  }

  const calculateTotal = () => {
    return formData.salary_components.reduce((total, sc) => {
      if (sc.component.component_type === 'earning') {
        return total + sc.value
      } else {
        return total - sc.value
      }
    }, 0)
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl w-full max-w-5xl max-h-[90vh] overflow-y-auto shadow-2xl">
        {/* Header */}
        <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold text-gray-900">
                {contractData ? 'Edit Contract' : 'Add New Contract'}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Configure contract details and terms
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              className="hover:bg-white/60"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>
        
        <div className="p-6 space-y-8">
          {/* Contract Type & Template Section */}
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FileText className="h-5 w-5 text-purple-600" />
              Contract Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700">Contract Type *</Label>
                <Select value={formData.contract_type_id} onValueChange={(value) => updateField('contract_type_id', value)}>
                  <SelectTrigger className="h-12 border-gray-200 focus:border-purple-500 focus:ring-purple-500">
                    <SelectValue placeholder="Select contract type" />
                  </SelectTrigger>
                  <SelectContent>
                    {contractTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        <div className="flex items-center gap-3 py-1">
                          <Building className="h-4 w-4 text-purple-600" />
                          {type.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700">Contract Template</Label>
                <Select value={formData.contract_template_id} onValueChange={(value) => updateField('contract_template_id', value)}>
                  <SelectTrigger className="h-12 border-gray-200 focus:border-purple-500 focus:ring-purple-500">
                    <SelectValue placeholder="Select template" />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredTemplates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        <div className="flex items-center gap-3 py-1">
                          <FileText className="h-4 w-4 text-orange-600" />
                          {template.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Timeline Section */}
          <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Calendar className="h-5 w-5 text-blue-600" />
              Timeline
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700">Start Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full h-12 justify-start border-gray-200 hover:border-blue-500">
                      <Calendar className="mr-3 h-4 w-4 text-blue-600" />
                      {formData.start_date ? format(formData.start_date, "PPP") : "Select start date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={formData.start_date || undefined}
                      onSelect={(date) => updateField('start_date', date || null)}
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700">End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full h-12 justify-start border-gray-200 hover:border-blue-500">
                      <Calendar className="mr-3 h-4 w-4 text-blue-600" />
                      {formData.end_date ? format(formData.end_date, "PPP") : "Select end date (optional)"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={formData.end_date || undefined}
                      onSelect={(date) => updateField('end_date', date || null)}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>

          {/* Terms & Status Section */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Settings className="h-5 w-5 text-green-600" />
              Contract Terms & Status
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700">Probation Period (months)</Label>
                <Input
                  type="number"
                  value={formData.probation_period}
                  onChange={(e) => updateField('probation_period', parseInt(e.target.value) || 0)}
                  className="h-12 border-gray-200 focus:border-green-500 focus:ring-green-500"
                />
              </div>
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700">Notice Period (days)</Label>
                <Input
                  type="number"
                  value={formData.notice_period}
                  onChange={(e) => updateField('notice_period', parseInt(e.target.value) || 0)}
                  className="h-12 border-gray-200 focus:border-green-500 focus:ring-green-500"
                />
              </div>
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700">Status</Label>
                <Select value={formData.status} onValueChange={(value) => updateField('status', value)}>
                  <SelectTrigger className="h-12 border-gray-200 focus:border-green-500 focus:ring-green-500">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                        Draft
                      </div>
                    </SelectItem>
                    <SelectItem value="active">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        Active
                      </div>
                    </SelectItem>
                    <SelectItem value="terminated">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        Terminated
                      </div>
                    </SelectItem>
                    <SelectItem value="suspended">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        Suspended
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-3">
                <div className="flex items-center space-x-3 p-4 bg-white/60 rounded-lg border border-gray-200">
                  <Checkbox
                    checked={formData.overtime_allowed}
                    onCheckedChange={(checked) => updateField('overtime_allowed', checked)}
                    className="data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                  />
                  <Label className="font-medium text-gray-800 cursor-pointer">
                    Allow Overtime
                  </Label>
                </div>
                {formData.overtime_allowed && (
                  <Input
                    type="number"
                    placeholder="Overtime rate"
                    value={formData.overtime_rate}
                    onChange={(e) => updateField('overtime_rate', parseFloat(e.target.value) || 0)}
                    className="h-10 border-gray-200 focus:border-green-500 focus:ring-green-500"
                  />
                )}
              </div>
            </div>
          </div>

          {/* Salary Components Section */}
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-6">
            <div className="flex items-center justify-between mb-6">
              <h4 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-yellow-600" />
                Salary Components
              </h4>
              <div className="text-right">
                <p className="text-sm text-gray-600">Total Salary</p>
                <p className="text-2xl font-bold text-green-600">₹{calculateTotal().toLocaleString()}</p>
              </div>
            </div>
            
            {/* Selected Components */}
            <div className="space-y-3 mb-6">
              {formData.salary_components.map((sc, index) => (
                <div key={index} className="flex items-center gap-4 p-4 bg-white/80 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex-1">
                    <span className="font-medium text-gray-900">{sc.component.name}</span>
                    <span className={`ml-3 px-3 py-1 text-xs font-medium rounded-full ${
                      sc.component.component_type === 'earning' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {sc.component.component_type}
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Input
                      type="number"
                      value={sc.value}
                      onChange={(e) => updateSalaryComponentValue(index, parseFloat(e.target.value) || 0)}
                      className="w-32 h-10 border-gray-200 focus:border-yellow-500 focus:ring-yellow-500"
                    />
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => removeSalaryComponent(index)}
                      className="hover:bg-red-50 hover:border-red-200 hover:text-red-600"
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Available Components */}
            <div className="border-t border-gray-200 pt-4">
              <Label className="text-sm font-medium text-gray-700 mb-3 block">Available Components</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-48 overflow-y-auto">
                {salaryComponents
                  .filter(comp => !formData.salary_components.find(sc => sc.salary_component_id === comp.id))
                  .map((component) => (
                    <div key={component.id} className="flex items-center justify-between p-3 bg-white/60 border border-gray-200 rounded-lg hover:bg-white/80 transition-colors">
                      <div>
                        <span className="text-sm font-medium text-gray-900">{component.name}</span>
                        <span className={`ml-2 px-2 py-0.5 text-xs font-medium rounded-full ${
                          component.component_type === 'earning' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {component.component_type}
                        </span>
                      </div>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        onClick={() => addSalaryComponent(component)}
                        className="hover:bg-green-50 hover:border-green-200 hover:text-green-600"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  )
                )}
              </div>
            </div>
          </div>

          {/* Holidays Section */}
          <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Calendar className="h-5 w-5 text-indigo-600" />
              Contract Holidays
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto">
              {holidays.map((holiday) => (
                <div key={holiday.id} className="flex items-center space-x-3 p-3 bg-white/60 rounded-lg border border-gray-200 hover:bg-white/80 transition-colors">
                  <Checkbox
                    checked={formData.selected_holidays.includes(holiday.id)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        updateField('selected_holidays', [...formData.selected_holidays, holiday.id])
                      } else {
                        updateField('selected_holidays', formData.selected_holidays.filter(id => id !== holiday.id))
                      }
                    }}
                    className="data-[state=checked]:bg-indigo-600 data-[state=checked]:border-indigo-600"
                  />
                  <div className="flex-1">
                    <Label className="text-sm font-medium text-gray-900 cursor-pointer">
                      {holiday.name}
                    </Label>
                    <p className="text-xs text-gray-600">
                      {format(new Date(holiday.start_date), "MMM dd")} - {format(new Date(holiday.end_date), "MMM dd")}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Leave Types Section */}
          <div className="bg-gradient-to-r from-teal-50 to-cyan-50 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Users className="h-5 w-5 text-teal-600" />
              Leave Configuration
            </h4>
            <div className="space-y-4 max-h-64 overflow-y-auto">
              {leaveTypes.map((leaveType) => {
                const selectedLeave = formData.selected_leaves.find(l => l.leave_type_id === leaveType.id)
                return (
                  <div key={leaveType.id} className="bg-white/60 border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-4">
                      <Checkbox
                        checked={!!selectedLeave}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            updateField('selected_leaves', [...formData.selected_leaves, {
                              leave_type_id: leaveType.id,
                              days_allowed: 0,
                              carry_forward: false,
                              encashable: false,
                              salary_payable: true
                            }])
                          } else {
                            updateField('selected_leaves', formData.selected_leaves.filter(l => l.leave_type_id !== leaveType.id))
                          }
                        }}
                        className="data-[state=checked]:bg-teal-600 data-[state=checked]:border-teal-600"
                      />
                      <Label className="font-medium text-gray-900 cursor-pointer">
                        {leaveType.name} ({leaveType.code})
                      </Label>
                    </div>
                    
                    {selectedLeave && (
                      <div className="ml-6 grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-white/80 rounded-lg border border-gray-100">
                        <div className="space-y-2">
                          <Label className="text-xs font-medium text-gray-700">Days Allowed</Label>
                          <Input
                            type="number"
                            value={selectedLeave.days_allowed}
                            onChange={(e) => {
                              const updated = formData.selected_leaves.map(l => 
                                l.leave_type_id === leaveType.id 
                                  ? {...l, days_allowed: parseInt(e.target.value) || 0}
                                  : l
                              )
                              updateField('selected_leaves', updated)
                            }}
                            className="h-9 text-sm border-gray-200 focus:border-teal-500 focus:ring-teal-500"
                          />
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={selectedLeave.carry_forward}
                            onCheckedChange={(checked) => {
                              const updated = formData.selected_leaves.map(l => 
                                l.leave_type_id === leaveType.id 
                                  ? {...l, carry_forward: checked as boolean}
                                  : l
                              )
                              updateField('selected_leaves', updated)
                            }}
                            className="data-[state=checked]:bg-teal-600 data-[state=checked]:border-teal-600"
                          />
                          <Label className="text-xs font-medium text-gray-700 cursor-pointer">Carry Forward</Label>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={selectedLeave.encashable}
                            onCheckedChange={(checked) => {
                              const updated = formData.selected_leaves.map(l => 
                                l.leave_type_id === leaveType.id 
                                  ? {...l, encashable: checked as boolean}
                                  : l
                              )
                              updateField('selected_leaves', updated)
                            }}
                            className="data-[state=checked]:bg-teal-600 data-[state=checked]:border-teal-600"
                          />
                          <Label className="text-xs font-medium text-gray-700 cursor-pointer">Encashable</Label>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={selectedLeave.salary_payable}
                            onCheckedChange={(checked) => {
                              const updated = formData.selected_leaves.map(l => 
                                l.leave_type_id === leaveType.id 
                                  ? {...l, salary_payable: checked as boolean}
                                  : l
                              )
                              updateField('selected_leaves', updated)
                            }}
                            className="data-[state=checked]:bg-teal-600 data-[state=checked]:border-teal-600"
                          />
                          <Label className="text-xs font-medium text-gray-700 cursor-pointer">Salary Payable</Label>
                        </div>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          </div>
        </div>
        
        {/* Footer */}
        <div className="p-6 border-t border-gray-100 bg-gray-50/50 flex gap-4">
          <Button 
            variant="outline" 
            onClick={onCancel} 
            className="flex-1 h-12 border-gray-300 hover:bg-gray-50"
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button 
            onClick={() => onSave(formData)} 
            disabled={!formData.contract_type_id || !formData.start_date}
            className="flex-1 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <Save className="h-4 w-4 mr-2" />
            {contractData ? 'Update Contract' : 'Add Contract'}
          </Button>
        </div>
      </div>
    </div>
  )
}
