import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { ArrowLeft, Eye, EyeOff } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"

export default function AddUser() {
  const navigate = useNavigate()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const [formData, setFormData] = useState({
    first_name: "",
    middle_name: "",
    last_name: "",
    personal_email: "",
    phone: "",
    date_of_birth: "",
    gender: "",
    city: "",
    state: "",
    address: "",
    pincode: "",
    role: "",
    password: "",
    company_email: "",
  })

  // Generate random password
  const generatePassword = () => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*"
    let password = ""
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    setFormData(prev => ({ ...prev, password }))
  }

  // Generate employee code
  const generateEmployeeCode = () => {
    const timestamp = Date.now().toString().slice(-6)
    const random = Math.floor(Math.random() * 100).toString().padStart(2, '0')
    return `EMP${timestamp}${random}`
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Validate required fields
      if (!formData.first_name || !formData.last_name || !formData.personal_email || !formData.role || !formData.password) {
        toast({
          title: "Error",
          description: "Please fill in all required fields",
          variant: "destructive",
        })
        return
      }

      // Create user in Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.personal_email,
        password: formData.password,
        options: {
          data: {
            first_name: formData.first_name,
            last_name: formData.last_name,
          }
        }
      })

      if (authError) throw authError
      if (!authData.user) throw new Error("Failed to create user")

      const userId = authData.user.id

      // Wait a moment for the trigger to create the profile
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Update user profile with additional data (profile was created by trigger)
      const profileData: any = {
        middle_name: formData.middle_name || null,
        phone: formData.phone || null,
        date_of_birth: formData.date_of_birth || null,
        city: formData.city || null,
        state: formData.state || null,
        address: formData.address || null,
        pincode: formData.pincode || null,
      }

      if (formData.gender && ['male', 'female', 'other'].includes(formData.gender)) {
        profileData.gender = formData.gender
      }

      const { error: profileError } = await supabase
        .from('user_profiles')
        .update(profileData)
        .eq('id', userId)

      if (profileError) throw profileError

      // Get role ID for the selected role (not admin)
      const { data: roleData, error: roleError } = await supabase
        .from('roles')
        .select('id')
        .eq('name', formData.role)
        .maybeSingle()

      if (roleError) {
        console.error('Role error:', roleError)
        throw new Error(`Role "${formData.role}" not found`)
      }

      if (!roleData) {
        throw new Error(`Role "${formData.role}" not found`)
      }

      // Delete any existing admin role assignment first
      await supabase
        .from('user_roles')
        .delete()
        .eq('user_id', userId)

      // Create user role with the selected role
      const { error: userRoleError } = await supabase
        .from('user_roles')
        .insert({
          user_id: userId,
          role_id: roleData.id,
        })

      if (userRoleError) throw userRoleError

      // Create employee record if role is Employee or HR Manager
      if (formData.role === 'Employee' || formData.role === 'HR Manager') {
        const employeeCode = generateEmployeeCode()
        const companyEmail = formData.company_email || `${formData.first_name.toLowerCase()}.${formData.last_name.toLowerCase()}@company.com`

        const { error: employeeError } = await supabase
          .from('employees')
          .insert({
            user_id: userId,
            employee_code: employeeCode,
            company_email: companyEmail,
            hire_date: new Date().toISOString().split('T')[0],
          })

        if (employeeError) throw employeeError
      }

      toast({
        title: "Success",
        description: "User created successfully",
      })

      navigate('/users')
    } catch (error: any) {
      console.error('Error creating user:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to create user",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Layout>
      <div className=" mx-auto space-y-1 p-2 md:p-2">
        <div className="flex flex-col sm:flex-row bg-[#4A9782] justify-between items-start sm:items-center gap-4  dark:bg-gray-800 rounded-lg p-6 text-white shadow-sm">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => navigate('/users')} className="hidden sm:flex">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl md:text-xl font-bold tracking-tight">Add New User</h1>
              <p className="text-muted-foreground mt-1 text-white">Create a new user account</p>
            </div>
          </div>
          <Button variant="outline" onClick={() => navigate('/users')} className="sm:hidden w-full">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
            {/* Personal Information */}
            <Card className="border-0 shadow-md">
              <CardHeader className="border-b bg-muted/30 rounded-t-lg">
                <CardTitle className="text-lg font-semibold">Personal Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="first_name" className="text-sm font-medium">
                      First Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="first_name"
                      value={formData.first_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, first_name: e.target.value }))}
                      className="mt-1.5"
                      placeholder="Enter first name"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="middle_name">Middle Name</Label>
                    <Input
                      id="middle_name"
                      value={formData.middle_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, middle_name: e.target.value }))}
                    />
                  </div>

                  <div>
                    <Label htmlFor="last_name" className="text-sm font-medium">
                      Last Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="last_name"
                      value={formData.last_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, last_name: e.target.value }))}
                      className="mt-1.5"
                      placeholder="Enter last name"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="personal_email" className="text-sm font-medium">
                      Personal Email <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="personal_email"
                      type="email"
                      value={formData.personal_email}
                      onChange={(e) => setFormData(prev => ({ ...prev, personal_email: e.target.value }))}
                      className="mt-1.5"
                      placeholder="Enter personal email"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="date_of_birth">Date of Birth</Label>
                    <Input
                      id="date_of_birth"
                      type="date"
                      value={formData.date_of_birth}
                      onChange={(e) => setFormData(prev => ({ ...prev, date_of_birth: e.target.value }))}
                    />
                  </div>

                  <div>
                    <Label htmlFor="gender">Gender</Label>
                    <Select value={formData.gender} onValueChange={(value) => setFormData(prev => ({ ...prev, gender: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Address & Account Details */}
            <Card className="border-0 shadow-md">
              <CardHeader className="border-b bg-muted/30 rounded-t-lg">
                <CardTitle className="text-lg font-semibold">Address & Account Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                <div>
                  <Label htmlFor="address">Address</Label>
                  <Textarea
                    id="address"
                    value={formData.address}
                    onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={formData.city}
                      onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="state">State</Label>
                    <Input
                      id="state"
                      value={formData.state}
                      onChange={(e) => setFormData(prev => ({ ...prev, state: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="pincode">Pincode</Label>
                    <Input
                      id="pincode"
                      value={formData.pincode}
                      onChange={(e) => setFormData(prev => ({ ...prev, pincode: e.target.value }))}
                    />
                  </div>
                </div>



                <div>
                  <Label htmlFor="role" className="text-sm font-medium">
                    Role <span className="text-red-500">*</span>
                  </Label>
                  <Select value={formData.role} onValueChange={(value) => setFormData(prev => ({ ...prev, role: value }))} required>
                    <SelectTrigger className="mt-1.5">
                      <SelectValue placeholder="Select user role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Employee">Employee</SelectItem>
                      <SelectItem value="HR Manager">HR Manager</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {(formData.role === 'Employee' || formData.role === 'HR Manager') && (
                  <div>
                    <Label htmlFor="company_email">Company Email</Label>
                    <Input
                      id="company_email"
                      type="email"
                      value={formData.company_email}
                      onChange={(e) => setFormData(prev => ({ ...prev, company_email: e.target.value }))}
                      placeholder={`${formData.first_name.toLowerCase()}.${formData.last_name.toLowerCase()}@company.com`}
                    />
                  </div>
                )}

                <div>
                  <Label htmlFor="password">Password *</Label>
                  <div className="flex space-x-2">
                    <div className="relative flex-1">
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        value={formData.password}
                        onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-2 top-1/2 transform -translate-y-1/2"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                    <Button type="button" variant="outline" onClick={generatePassword}>
                      Generate
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="border-0 shadow-md">
            <CardContent className="flex flex-col sm:flex-row justify-end gap-4 py-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/users')}
                className="w-full sm:w-auto order-1 sm:order-none"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className="w-full sm:w-auto bg-primary hover:bg-primary/90"
              >
                {loading ? "Creating..." : "Create User"}
              </Button>
            </CardContent>
          </Card>
        </form>
      </div >
    </Layout >
  )
}