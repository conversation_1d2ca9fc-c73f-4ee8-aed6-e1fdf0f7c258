import { useState, useEffect } from "react"
import { Check, X, FileText, User, Calendar } from "lucide-react"
import { Layout } from "@/components/Layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/integrations/supabase/client"
import { format } from "date-fns"

export default function HRApproval() {
  const { toast } = useToast()
  const [pendingEmployees, setPendingEmployees] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [rejectionReason, setRejectionReason] = useState("")
  const [selectedEmployee, setSelectedEmployee] = useState<string | null>(null)

  useEffect(() => {
    fetchPendingEmployees()
  }, [])

  const fetchPendingEmployees = async () => {
    try {
      console.log('Fetching employees with docs_uploaded status...')
      
      // Get employees with docs_uploaded status
      const { data: employees, error } = await supabase
        .from('employees')
        .select('id, employee_code, onboarding_status, created_at, user_id')
        .eq('onboarding_status', 'docs_uploaded')
        .eq('is_active', true)
        .eq('is_deleted', false)

      console.log('Employees found:', employees)
      if (error) {
        console.error('Error fetching employees:', error)
        throw error
      }

      if (!employees || employees.length === 0) {
        console.log('No employees with docs_uploaded status found')
        setPendingEmployees([])
        return
      }

      // Get user profiles
      const userIds = employees.map(emp => emp.user_id)
      const employeeIds = employees.map(emp => emp.id)
      
      const { data: profiles } = await supabase
        .from('user_profiles')
        .select('id, first_name, last_name')
        .in('id', userIds)

      // Get contract acceptances - remove is_accepted filter to see all
    const { data: acceptances } = await supabase
  .from('user_contract_acceptance')
  .select(`
    id,
    accepted_at,
    contract_id,
    user_id,
    employee_id,
    is_accepted,
    contracts (
      id,
      contract_types (
        id,
        name
      )
    )
  `)
  .in('user_id', userIds)
  console.log("User IDs used in query:", userIds)

console.log("User Acceptances:", acceptances)


      console.log('Contract acceptances found:', acceptances)

      // Get documents
      const { data: documents } = await supabase
        .from('employee_documents')
        .select('employee_id, document_type, file_url, is_skipped, skip_reason, uploaded_at')
        .in('employee_id', employeeIds)

      console.log('Documents found:', documents)

      // Combine data
      const data = employees.map(employee => ({
        ...employee,
        user_profiles: profiles?.find(p => p.id === employee.user_id) || { first_name: '', last_name: '' },
        user_contract_acceptance: acceptances?.filter(a => a.user_id === employee.user_id && a.is_accepted === true) || [],
        employee_documents: documents?.filter(d => d.employee_id === employee.id) || []
      }))

      console.log('Final combined data:', data)
      setPendingEmployees(data)
    } catch (error) {
      console.error('Error fetching pending employees:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleApproval = async (employeeId: string, approved: boolean) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')

      const { error } = await supabase.rpc('hr_approve_onboarding', {
        p_employee_id: employeeId,
        p_approved: approved,
        p_hr_user_id: user.id,
        p_rejection_reason: approved ? null : rejectionReason
      })

      if (error) throw error

      toast({
        title: approved ? "Employee Approved" : "Employee Rejected",
        description: approved 
          ? "Employee can now access the system" 
          : "Employee has been notified of rejection",
      })

      setRejectionReason("")
      setSelectedEmployee(null)
      fetchPendingEmployees()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      })
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">Loading...</div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">HR Approval Dashboard</h1>
          <p className="text-gray-600">Review and approve employee onboarding</p>
        </div>

        {pendingEmployees.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Pending Approvals</h3>
              <p className="text-gray-600">All employees have been processed</p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {pendingEmployees.map((employee) => (
              <Card key={employee.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-3">
                        <User className="h-5 w-5" />
                        {employee.user_profiles.first_name} {employee.user_profiles.last_name}
                      </CardTitle>
                      <p className="text-sm text-gray-600">
                        {employee.employee_code} • Submitted {format(new Date(employee.created_at), 'PPP')}
                      </p>
                    </div>
                    <Badge variant="secondary">
                      {employee.onboarding_status.replace('_', ' ')}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  {/* Contract Acceptance */}
                  <div>
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Contract Acceptance
                    </h4>
                    {employee.user_contract_acceptance && employee.user_contract_acceptance.length > 0 ? (
                      <div className="bg-green-50 p-3 rounded">
                        <p className="text-sm text-green-800">
                          ✓ Accepted on {format(new Date(employee.user_contract_acceptance[0].accepted_at), 'PPP')}
                        </p>
                        <p className="text-xs text-green-600">
                          Contract Type: {
    employee.user_contract_acceptance[0]?.contracts?.contract_types?.name || 'Unknown'
  }
                        </p>
                      </div>
                    ) : (
                      <div className="bg-red-50 p-3 rounded">
                        <p className="text-sm text-red-800">✗ Contract not accepted</p>
                      </div>
                    )}
                  </div>

                  {/* Documents */}
                  <div>
                    <h4 className="font-medium mb-2">Documents</h4>
                    <div className="space-y-2">
                      {employee.employee_documents && employee.employee_documents.length > 0 ? employee.employee_documents.map((doc: any, index: number) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                          <div>
                            <p className="font-medium text-sm">{doc.document_type}</p>
                            {doc.is_skipped ? (
                              <p className="text-xs text-orange-600">Skipped: {doc.skip_reason}</p>
                            ) : (
                              <p className="text-xs text-green-600">
                                Uploaded on {format(new Date(doc.uploaded_at), 'PPP')}
                              </p>
                            )}
                          </div>
                          {!doc.is_skipped && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.open(doc.file_url, '_blank')}
                            >
                              View
                            </Button>
                          )}
                        </div>
                      )) : (
                        <div className="p-3 bg-gray-50 rounded text-center">
                          <p className="text-sm text-gray-600">No documents uploaded</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Approval Actions */}
                  <div className="border-t pt-4">
                    <div className="flex gap-4">
                      <Button
                        onClick={() => handleApproval(employee.id, true)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Check className="h-4 w-4 mr-2" />
                        Approve
                      </Button>
                      
                      <Button
                        variant="outline"
                        onClick={() => setSelectedEmployee(employee.id)}
                      >
                        <X className="h-4 w-4 mr-2" />
                        Reject
                      </Button>
                    </div>

                    {selectedEmployee === employee.id && (
                      <div className="mt-4 space-y-3">
                        <Textarea
                          placeholder="Reason for rejection..."
                          value={rejectionReason}
                          onChange={(e) => setRejectionReason(e.target.value)}
                        />
                        <div className="flex gap-2">
                          <Button
                            variant="destructive"
                            onClick={() => handleApproval(employee.id, false)}
                            disabled={!rejectionReason.trim()}
                          >
                            Confirm Rejection
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => {
                              setSelectedEmployee(null)
                              setRejectionReason("")
                            }}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </Layout>
  )
}