import { useState, useEffect } from "react"
import { <PERSON>, use<PERSON>avi<PERSON> } from "react-router-dom"
import { Plus, Search, Edit, Trash2, Eye, MoreHorizontal } from "lucide-react"
import { Layout } from "@/components/Layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Switch } from "@/components/ui/switch"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"

interface ContractType {
  id: string
  name: string
  code: string
  description: string
  is_active: boolean
  created_at: string
  required_documents: Array<{
    id: string
    document_type: string
    is_mandatory: boolean
    remarks: string
  }>
  templates: Array<{
    id: string
    name: string
    content: string
    variables: any
  }>
}

export default function ContractTypes() {
  const [contractTypes, setContractTypes] = useState<ContractType[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)
  const [selectedContract, setSelectedContract] = useState<ContractType | null>(null)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  const { toast } = useToast()
  const navigate = useNavigate()

  useEffect(() => {
    fetchContractTypes()
  }, [])

  const fetchContractTypes = async () => {
    try {
      const { data: contractData, error } = await supabase
        .from('contract_types')
        .select(`
          id,
          name,
          code,
          description,
          is_active,
          created_at
        `)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Get contract type IDs
      const contractIds = contractData?.map(ct => ct.id) || []
      
      // Fetch required documents
      const { data: documents } = contractIds.length > 0 ? await supabase
        .from('contract_type_required_documents')
        .select('id, contract_type_id, document_type, is_mandatory, remarks')
        .in('contract_type_id', contractIds)
        .eq('is_active', true) : { data: [] }

      // Fetch templates
      const { data: templates } = contractIds.length > 0 ? await supabase
        .from('contract_templates')
        .select('id, contract_type_id, name, content, variables')
        .in('contract_type_id', contractIds)
        .eq('is_active', true) : { data: [] }

      // Combine data
      const combinedData = contractData?.map(contract => ({
        ...contract,
        required_documents: documents?.filter(doc => doc.contract_type_id === contract.id) || [],
        templates: templates?.filter(temp => temp.contract_type_id === contract.id) || []
      })) || []

      setContractTypes(combinedData)
    } catch (error) {
      console.error('Error fetching contract types:', error)
      toast({
        title: "Error",
        description: "Failed to fetch contract types",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const filteredContracts = contractTypes.filter(contract =>
    contract.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contract.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contract.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleView = (contract: ContractType) => {
    setSelectedContract(contract)
    setViewDialogOpen(true)
  }

  const handleEdit = (contractId: string) => {
    navigate(`/masters/contract-types/edit/${contractId}`)
  }

  const handleDelete = async (contractId: string) => {
    if (confirm('Are you sure you want to delete this contract type?')) {
      try {
        const { error } = await supabase
          .from('contract_types')
          .update({ is_deleted: true })
          .eq('id', contractId)
        
        if (error) throw error
        
        toast({
          title: "Success",
          description: "Contract type deleted successfully",
        })
        fetchContractTypes()
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete contract type",
          variant: "destructive",
        })
      }
    }
  }

  const handleToggleStatus = async (contractId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('contract_types')
        .update({ is_active: !currentStatus })
        .eq('id', contractId)
      
      if (error) throw error
      
      toast({
        title: "Success",
        description: `Contract type ${!currentStatus ? 'activated' : 'deactivated'} successfully`,
      })
      fetchContractTypes()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update contract type status",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Loading contract types...</div>
      </div>
    )
  }

  return (
    <Layout>
      <div className="space-y-6 p-4 md:p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Contract Types</h1>
            <p className="text-muted-foreground">Manage contract types and templates</p>
          </div>
          <Link to="/masters/contract-types/add">
            <Button className="w-full sm:w-auto">
              <Plus className="h-4 w-4 mr-2" />
              Add Contract Type
            </Button>
          </Link>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Contract Types List</CardTitle>
            <div className="flex items-center space-x-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search contract types..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>
          </CardHeader>
          <CardContent className="p-0">
            {/* Mobile Card View */}
            <div className="block md:hidden">
              {filteredContracts.map((contract) => (
                <div key={contract.id} className="border-b p-4 space-y-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{contract.name}</h3>
                      <p className="text-sm text-muted-foreground">Code: {contract.code}</p>
                      <p className="text-sm text-muted-foreground">{contract.description}</p>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleView(contract)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEdit(contract.id)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDelete(contract.id)} className="text-red-600">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={contract.is_active}
                      onCheckedChange={() => handleToggleStatus(contract.id, contract.is_active)}
                    />
                    <span className="text-sm">{contract.is_active ? "Active" : "Inactive"}</span>
                  </div>
                </div>
              ))}
              {filteredContracts.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No contract types found
                </div>
              )}
            </div>

            {/* Desktop Table View */}
            <div className="hidden md:block">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Documents</TableHead>
                    <TableHead>Templates</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredContracts.map((contract) => (
                    <TableRow key={contract.id}>
                      <TableCell className="font-medium">{contract.name}</TableCell>
                      <TableCell className="font-mono text-sm">{contract.code}</TableCell>
                      <TableCell className="max-w-xs truncate">{contract.description}</TableCell>
                      <TableCell>{contract.required_documents.length} docs</TableCell>
                      <TableCell>{contract.templates.length} templates</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={contract.is_active}
                            onCheckedChange={() => handleToggleStatus(contract.id, contract.is_active)}
                          />
                          <span className="text-sm">{contract.is_active ? "Active" : "Inactive"}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm" onClick={() => handleView(contract)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" onClick={() => handleEdit(contract.id)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" onClick={() => handleDelete(contract.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                  {filteredContracts.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        No contract types found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* View Contract Dialog */}
        <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Contract Type Details</DialogTitle>
            </DialogHeader>
            {selectedContract && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Name</label>
                    <p className="text-sm text-muted-foreground">{selectedContract.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Code</label>
                    <p className="text-sm text-muted-foreground font-mono">{selectedContract.code}</p>
                  </div>
                  <div className="md:col-span-2">
                    <label className="text-sm font-medium">Description</label>
                    <p className="text-sm text-muted-foreground">{selectedContract.description}</p>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium">Status</label>
                  <div className="mt-1">
                    <Badge variant={selectedContract.is_active ? "default" : "secondary"}>
                      {selectedContract.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium">Required Documents ({selectedContract.required_documents.length})</label>
                  <div className="mt-2 space-y-2">
                    {selectedContract.required_documents.map((doc) => (
                      <div key={doc.id} className="p-2 bg-gray-50 rounded space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{doc.document_type}</span>
                          <Badge variant={doc.is_mandatory ? "destructive" : "secondary"}>
                            {doc.is_mandatory ? "Mandatory" : "Optional"}
                          </Badge>
                        </div>
                        {doc.remarks && (
                          <p className="text-xs text-muted-foreground">{doc.remarks}</p>
                        )}
                      </div>
                    ))}
                    {selectedContract.required_documents.length === 0 && (
                      <p className="text-sm text-muted-foreground">No required documents</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium">Templates ({selectedContract.templates.length})</label>
                  <div className="mt-2 space-y-2">
                    {selectedContract.templates.map((template) => (
                      <div key={template.id} className="p-2 bg-gray-50 rounded space-y-1">
                        <p className="text-sm font-medium">{template.name}</p>
                        <p className="text-xs text-muted-foreground truncate">
                          {template.content.substring(0, 100)}...
                        </p>
                        {template.variables && (
                          <p className="text-xs text-blue-600">Variables: {JSON.stringify(template.variables)}</p>
                        )}
                      </div>
                    ))}
                    {selectedContract.templates.length === 0 && (
                      <p className="text-sm text-muted-foreground">No templates</p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  )
}