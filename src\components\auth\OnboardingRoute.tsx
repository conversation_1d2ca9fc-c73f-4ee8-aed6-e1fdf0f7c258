import { useEffect, useState } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { supabase } from '@/integrations/supabase/client'
import { useAuth } from '@/contexts/AuthContext'

interface OnboardingRouteProps {
  children: React.ReactNode
  requiredStatus?: string[]
}

export const OnboardingRoute = ({ children, requiredStatus = [] }: OnboardingRouteProps) => {
  const { user, loading: authLoading } = useAuth()
  const location = useLocation()
  const [loading, setLoading] = useState(true)
  const [hasAccess, setHasAccess] = useState(false)

  useEffect(() => {
    if (authLoading) return
    
    if (!user) {
      setLoading(false)
      return
    }

    checkOnboardingAccess()
  }, [user, authLoading])

  const checkOnboardingAccess = async () => {
    if (!user) return

    try {
      const { data: onboardingData, error } = await supabase
        .rpc('get_employee_onboarding_status', {
          p_user_id: user.id
        })

      if (error) {
        console.error('Error checking onboarding status:', error)
        // If function fails, deny access to onboarding pages
        setHasAccess(false)
        setLoading(false)
        return
      }

      if (!onboardingData) {
        // Not an employee, deny access
        setHasAccess(false)
        setLoading(false)
        return
      }

      // Check if user has access to this specific onboarding page
      if (requiredStatus.length === 0 || requiredStatus.includes(onboardingData.onboarding_status)) {
        setHasAccess(true)
      } else {
        setHasAccess(false)
      }

      setLoading(false)
    } catch (error) {
      console.error('Onboarding access check error:', error)
      setHasAccess(false)
      setLoading(false)
    }
  }

  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  if (!hasAccess) {
    return <Navigate to="/" replace />
  }

  return <>{children}</>
}
