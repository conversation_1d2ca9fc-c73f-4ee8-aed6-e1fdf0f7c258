import { useState, useEffect } from "react"
import { User, FileText, Calendar, CreditCard, Bell, Clock } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/contexts/AuthContext"
import { supabase } from "@/integrations/supabase/client"
import { format } from "date-fns"

export default function EmployeeDashboard() {
  const { user } = useAuth()
  const [employee, setEmployee] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      fetchEmployeeData()
    }
  }, [user])

  const fetchEmployeeData = async () => {
    try {
      // Get employee data
      const { data: employeeData } = await supabase
        .from('employees')
        .select(`
          *,
          user_profiles(*),
          departments(name, code),
          designations(title, code),
          employee_bank_details(*),
          contract_groups(
            id,
            name,
            start_date,
            end_date,
            status,
            contracts(
              id,
              start_date,
              end_date,
              basic_salary,
              status,
              contract_types(name, code),
              employee_salary_components(
                value,
                salary_components(name, component_type)
              )
            )
          ),
          employee_documents(
            id,
            document_type,
            file_url,
            uploaded_at,
            is_verified
          )
        `)
        .eq('user_id', user?.id)
        .single()

      setEmployee(employeeData)
    } catch (error) {
      console.error('Error fetching employee data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading...</div>
        </div>
      </Layout>
    )
  }

  if (!employee) {
    return (
      <Layout>
        <div className="text-center py-8">
          <p>Employee data not found</p>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="p-6 space-y-6">
        {/* Welcome Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
              <User className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">
                Welcome, {employee.user_profiles.first_name} {employee.user_profiles.last_name}
              </h1>
              <p className="opacity-90">
                {employee.designations?.title} • {employee.departments?.name}
              </p>
              <p className="text-sm opacity-75">
                Employee ID: {employee.employee_code}
              </p>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Clock className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Status</p>
                  <Badge variant={employee.employment_status === 'active' ? 'default' : 'secondary'}>
                    {employee.employment_status}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Calendar className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Hire Date</p>
                  <p className="font-medium">{format(new Date(employee.hire_date), 'MMM dd, yyyy')}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <FileText className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Contracts</p>
                  <p className="font-medium">
                    {employee.contract_groups?.reduce((total: number, group: any) => 
                      total + (group.contracts?.length || 0), 0) || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Bell className="h-8 w-8 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600">Onboarding</p>
                  <Badge variant={employee.onboarding_status === 'approved' ? 'default' : 'secondary'}>
                    {employee.onboarding_status}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile">My Profile</TabsTrigger>
            <TabsTrigger value="contracts">My Contracts</TabsTrigger>
            <TabsTrigger value="documents">My Documents</TabsTrigger>
            <TabsTrigger value="banking">Banking Info</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Personal Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Full Name</label>
                    <p className="text-sm text-muted-foreground">
                      {employee.user_profiles.first_name} {employee.user_profiles.middle_name} {employee.user_profiles.last_name}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Email</label>
                    <p className="text-sm text-muted-foreground">{employee.company_email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Phone</label>
                    <p className="text-sm text-muted-foreground">{employee.user_profiles.phone || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Department</label>
                    <p className="text-sm text-muted-foreground">{employee.departments?.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Designation</label>
                    <p className="text-sm text-muted-foreground">{employee.designations?.title}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Address</label>
                    <p className="text-sm text-muted-foreground">{employee.user_profiles.address || 'N/A'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="contracts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>My Employment Contracts</CardTitle>
              </CardHeader>
              <CardContent>
                {employee.contract_groups && employee.contract_groups.length > 0 ? (
                  <div className="space-y-4">
                    {employee.contract_groups.map((group: any) => (
                      <div key={group.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="font-medium">{group.name}</h3>
                          <Badge variant={group.status === 'active' ? 'default' : 'secondary'}>
                            {group.status}
                          </Badge>
                        </div>
                        
                        {group.contracts?.map((contract: any) => (
                          <div key={contract.id} className="bg-gray-50 rounded p-3 mb-2">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                              <div>
                                <label className="text-xs font-medium text-gray-600">Contract Type</label>
                                <p className="text-sm">{contract.contract_types?.name}</p>
                              </div>
                              <div>
                                <label className="text-xs font-medium text-gray-600">Period</label>
                                <p className="text-sm">
                                  {format(new Date(contract.start_date), 'MMM yyyy')} - 
                                  {contract.end_date ? format(new Date(contract.end_date), 'MMM yyyy') : 'Ongoing'}
                                </p>
                              </div>
                              <div>
                                <label className="text-xs font-medium text-gray-600">Salary</label>
                                <p className="text-sm font-medium text-green-600">
                                  ₹{contract.basic_salary.toLocaleString()}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center text-muted-foreground py-8">No contracts found</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>My Documents</CardTitle>
              </CardHeader>
              <CardContent>
                {employee.employee_documents && employee.employee_documents.length > 0 ? (
                  <div className="space-y-3">
                    {employee.employee_documents.map((doc: any) => (
                      <div key={doc.id} className="flex items-center justify-between border rounded p-3">
                        <div>
                          <p className="font-medium">{doc.document_type}</p>
                          <p className="text-sm text-muted-foreground">
                            Uploaded: {format(new Date(doc.uploaded_at), 'PPP')}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={doc.is_verified ? 'default' : 'secondary'}>
                            {doc.is_verified ? 'Verified' : 'Pending'}
                          </Badge>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(doc.file_url, '_blank')}
                          >
                            View
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center text-muted-foreground py-8">No documents found</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="banking" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Banking Information</CardTitle>
              </CardHeader>
              <CardContent>
                {employee.employee_bank_details && employee.employee_bank_details.length > 0 ? (
                  <div className="space-y-4">
                    {employee.employee_bank_details.map((bank: any, index: number) => (
                      <div key={index} className="border rounded p-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium">Bank Name</label>
                            <p className="text-sm text-muted-foreground">{bank.bank_name}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium">Account Holder</label>
                            <p className="text-sm text-muted-foreground">{bank.account_holder_name}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium">Account Number</label>
                            <p className="text-sm text-muted-foreground font-mono">
                              ****{bank.account_number.slice(-4)}
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium">IFSC Code</label>
                            <p className="text-sm text-muted-foreground font-mono">{bank.ifsc_code}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center text-muted-foreground py-8">No banking information found</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  )
}