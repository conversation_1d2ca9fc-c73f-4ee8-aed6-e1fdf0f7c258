import { useState, useEffect } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { ArrowLeft, Save } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"

export default function EditSalaryComponent() {
  const { id } = useParams()
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    component_type: "",
    calculation_type: "",
    default_value: "",
    percentage_of: "",
    formula: "",
    description: "",
    is_taxable: true,
    is_active: true
  })
  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const { toast } = useToast()
  const navigate = useNavigate()

  useEffect(() => {
    if (id) {
      fetchComponent()
    }
  }, [id])

  const fetchComponent = async () => {
    try {
      const { data, error } = await supabase
        .from('salary_components')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error

      setFormData({
        name: data.name,
        code: data.code,
        component_type: data.component_type,
        calculation_type: data.calculation_type,
        default_value: data.default_value?.toString() || "",
        percentage_of: data.percentage_of || "",
        formula: data.formula || "",
        description: data.description || "",
        is_taxable: data.is_taxable,
        is_active: data.is_active
      })
    } catch (error) {
      console.error('Error fetching salary component:', error)
      toast({
        title: "Error",
        description: "Failed to fetch salary component",
        variant: "destructive",
      })
      navigate('/masters/salary-components')
    } finally {
      setInitialLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name || !formData.code || !formData.component_type || !formData.calculation_type) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    try {
      const { error } = await supabase
        .from('salary_components')
        .update({
          name: formData.name,
          code: formData.code.toUpperCase(),
          component_type: formData.component_type,
          calculation_type: formData.calculation_type,
          default_value: formData.default_value ? parseFloat(formData.default_value) : null,
          percentage_of: formData.percentage_of || null,
          formula: formData.formula || null,
          description: formData.description || null,
          is_taxable: formData.is_taxable,
          is_active: formData.is_active
        })
        .eq('id', id)

      if (error) throw error

      toast({
        title: "Success",
        description: "Salary component updated successfully",
      })
      navigate('/masters/salary-components')
    } catch (error: any) {
      console.error('Error updating salary component:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to update salary component",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  if (initialLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading salary component...</div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6 p-4 md:p-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => navigate('/masters/salary-components')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Edit Salary Component</h1>
            <p className="text-muted-foreground">Update salary component details</p>
          </div>
        </div>

        <Card className="max-w-2xl">
          <CardHeader>
            <CardTitle>Component Details</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="e.g., Basic Salary"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="code">Code *</Label>
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
                    placeholder="e.g., BASIC"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="component_type">Component Type *</Label>
                  <Select value={formData.component_type} onValueChange={(value) => handleInputChange('component_type', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select component type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="earning">Earning</SelectItem>
                      <SelectItem value="deduction">Deduction</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="calculation_type">Calculation Type *</Label>
                  <Select value={formData.calculation_type} onValueChange={(value) => handleInputChange('calculation_type', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select calculation type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fixed">Fixed Amount</SelectItem>
                      <SelectItem value="percentage">Percentage</SelectItem>
                      <SelectItem value="formula">Formula Based</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {formData.calculation_type === 'fixed' && (
                  <div className="space-y-2">
                    <Label htmlFor="default_value">Default Value</Label>
                    <Input
                      id="default_value"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.default_value}
                      onChange={(e) => handleInputChange('default_value', e.target.value)}
                      placeholder="0.00"
                    />
                  </div>
                )}

                {formData.calculation_type === 'percentage' && (
                  <div className="space-y-2">
                    <Label htmlFor="percentage_of">Percentage Of</Label>
                    <Input
                      id="percentage_of"
                      value={formData.percentage_of}
                      onChange={(e) => handleInputChange('percentage_of', e.target.value)}
                      placeholder="e.g., BASIC"
                    />
                  </div>
                )}
              </div>

              {formData.calculation_type === 'formula' && (
                <div className="space-y-2">
                  <Label htmlFor="formula">Formula</Label>
                  <Textarea
                    id="formula"
                    value={formData.formula}
                    onChange={(e) => handleInputChange('formula', e.target.value)}
                    placeholder="Enter calculation formula"
                    rows={3}
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Component description"
                  rows={2}
                />
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="is_taxable">Taxable</Label>
                    <p className="text-sm text-muted-foreground">Subject to tax calculations</p>
                  </div>
                  <Switch
                    id="is_taxable"
                    checked={formData.is_taxable}
                    onCheckedChange={(checked) => handleInputChange('is_taxable', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="is_active">Active</Label>
                    <p className="text-sm text-muted-foreground">Currently active component</p>
                  </div>
                  <Switch
                    id="is_active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => handleInputChange('is_active', checked)}
                  />
                </div>
              </div>

              <div className="flex gap-4 pt-4">
                <Button type="submit" disabled={loading}>
                  <Save className="h-4 w-4 mr-2" />
                  {loading ? "Updating..." : "Update Component"}
                </Button>
                <Button type="button" variant="outline" onClick={() => navigate('/masters/salary-components')}>
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </Layout>
  )
}