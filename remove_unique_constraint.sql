-- Remove the unique constraint to allow soft delete approach
ALTER TABLE public.employee_salary_components 
DROP CONSTRAINT unique_employee_salary_component;

-- Add new unique constraint that includes is_deleted to allow soft deletes
ALTER TABLE public.employee_salary_components 
ADD CONSTRAINT unique_employee_salary_component_active 
UNIQUE (employee_id, salary_component_id, effective_from, is_deleted);