import { useState, useEffect } from "react"
import { User, FileText, Calendar, CreditCard, Clock, Building, Edit } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/contexts/AuthContext"
import { supabase } from "@/integrations/supabase/client"
import { format } from "date-fns"

export default function MyProfile() {
  const { user } = useAuth()
  const [employee, setEmployee] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      fetchEmployeeProfile()
    }
  }, [user])

  const fetchEmployeeProfile = async () => {
    try {
      // Get complete employee profile
      const { data: employeeData } = await supabase
        .from('employees')
        .select(`
          *,
          user_profiles(*),
          departments(name, code),
          designations(title, code),
          employee_bank_details(*),
          employee_shifts(
            shift_id,
            work_week_id,
            shifts(name, start_time, end_time),
            work_weeks(name, days)
          ),
          contract_groups(
            id,
            name,
            start_date,
            end_date,
            status,
            contracts(
              id,
              start_date,
              end_date,
              basic_salary,
              status,
              probation_period,
              notice_period,
              overtime_allowed,
              overtime_rate,
              contract_types(name, code),
              employee_salary_components(
                value,
                salary_components(name, component_type)
              )
            )
          ),
          employee_documents(
            id,
            document_type,
            file_url,
            uploaded_at,
            is_verified
          )
        `)
        .eq('user_id', user?.id)
        .single()

      setEmployee(employeeData)
    } catch (error) {
      console.error('Error fetching employee profile:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading profile...</div>
        </div>
      </Layout>
    )
  }

  if (!employee) {
    return (
      <Layout>
        <div className="text-center py-8">
          <p>Profile not found</p>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="p-6 space-y-6">
        {/* Profile Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center">
                <User className="h-10 w-10" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">
                  {employee.user_profiles.first_name} {employee.user_profiles.last_name}
                </h1>
                <p className="text-xl opacity-90">
                  {employee.designations?.title} • {employee.departments?.name}
                </p>
                <p className="opacity-75">
                  Employee ID: {employee.employee_code}
                </p>
              </div>
            </div>
            <Button variant="secondary" className="text-gray-900">
              <Edit className="h-4 w-4 mr-2" />
              Edit Profile
            </Button>
          </div>
        </div>

        {/* Profile Content */}
        <Tabs defaultValue="personal" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="personal">Personal</TabsTrigger>
            <TabsTrigger value="work">Work Details</TabsTrigger>
            <TabsTrigger value="contracts">Contracts</TabsTrigger>
            <TabsTrigger value="shifts">Shifts</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
          </TabsList>

          <TabsContent value="personal" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">First Name</label>
                      <p className="font-medium">{employee.user_profiles.first_name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Last Name</label>
                      <p className="font-medium">{employee.user_profiles.last_name}</p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Email</label>
                    <p className="font-medium">{employee.user_profiles.personal_email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Phone</label>
                    <p className="font-medium">{employee.user_profiles.phone || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Date of Birth</label>
                    <p className="font-medium">
                      {employee.user_profiles.date_of_birth ? 
                        format(new Date(employee.user_profiles.date_of_birth), 'PPP') : 'N/A'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Gender</label>
                    <p className="font-medium">{employee.user_profiles.gender || 'N/A'}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Address Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Address</label>
                    <p className="font-medium">{employee.user_profiles.address || 'N/A'}</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">City</label>
                      <p className="font-medium">{employee.user_profiles.city || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">State</label>
                      <p className="font-medium">{employee.user_profiles.state || 'N/A'}</p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Pincode</label>
                    <p className="font-medium">{employee.user_profiles.pincode || 'N/A'}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="work" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Employment Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Employee Code</label>
                    <p className="font-medium">{employee.employee_code}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Company Email</label>
                    <p className="font-medium">{employee.company_email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Department</label>
                    <p className="font-medium">{employee.departments?.name} ({employee.departments?.code})</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Designation</label>
                    <p className="font-medium">{employee.designations?.title} ({employee.designations?.code})</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Hire Date</label>
                    <p className="font-medium">{format(new Date(employee.hire_date), 'PPP')}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Employment Status</label>
                    <Badge variant={employee.employment_status === 'active' ? 'default' : 'secondary'}>
                      {employee.employment_status}
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Banking Information</CardTitle>
                </CardHeader>
                <CardContent>
                  {employee.employee_bank_details && employee.employee_bank_details.length > 0 ? (
                    <div className="space-y-4">
                      {employee.employee_bank_details.map((bank: any, index: number) => (
                        <div key={index} className="border rounded p-3">
                          <div className="grid grid-cols-2 gap-3">
                            <div>
                              <label className="text-sm font-medium text-gray-600">Bank Name</label>
                              <p className="font-medium">{bank.bank_name}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-600">Account Holder</label>
                              <p className="font-medium">{bank.account_holder_name}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-600">Account Number</label>
                              <p className="font-medium font-mono">****{bank.account_number.slice(-4)}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-600">IFSC Code</label>
                              <p className="font-medium font-mono">{bank.ifsc_code}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500">No banking information available</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="contracts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Employment Contracts</CardTitle>
              </CardHeader>
              <CardContent>
                {employee.contract_groups && employee.contract_groups.length > 0 ? (
                  <div className="space-y-6">
                    {employee.contract_groups.map((group: any) => (
                      <div key={group.id} className="border-2 border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="text-lg font-semibold">{group.name}</h3>
                          <Badge variant={group.status === 'active' ? 'default' : 'secondary'}>
                            {group.status}
                          </Badge>
                        </div>
                        
                        {group.contracts?.map((contract: any) => (
                          <div key={contract.id} className="bg-gray-50 rounded p-4 mb-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div>
                                <label className="text-sm font-medium text-gray-600">Contract Type</label>
                                <p className="font-medium">{contract.contract_types?.name}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-gray-600">Period</label>
                                <p className="font-medium">
                                  {format(new Date(contract.start_date), 'MMM yyyy')} - 
                                  {contract.end_date ? format(new Date(contract.end_date), 'MMM yyyy') : 'Ongoing'}
                                </p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-gray-600">Basic Salary</label>
                                <p className="font-medium text-green-600">₹{contract.basic_salary.toLocaleString()}</p>
                              </div>
                            </div>
                            
                            {contract.employee_salary_components && contract.employee_salary_components.length > 0 && (
                              <div className="mt-4 pt-3 border-t">
                                <label className="text-sm font-medium text-gray-600 block mb-2">Salary Components</label>
                                <div className="grid grid-cols-2 gap-2">
                                  {contract.employee_salary_components.map((sc: any, index: number) => (
                                    <div key={index} className="flex justify-between p-2 bg-white rounded">
                                      <span className="text-sm">{sc.salary_components.name}</span>
                                      <span className="text-sm font-medium">₹{sc.value.toLocaleString()}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">No contracts found</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="shifts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Work Schedule</CardTitle>
              </CardHeader>
              <CardContent>
                {employee.employee_shifts && employee.employee_shifts.length > 0 ? (
                  <div className="space-y-4">
                    {employee.employee_shifts.map((shift: any, index: number) => (
                      <div key={index} className="border rounded p-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <label className="text-sm font-medium text-gray-600">Shift Name</label>
                            <p className="font-medium">{shift.shifts?.name}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">Timing</label>
                            <p className="font-medium">
                              {shift.shifts?.start_time} - {shift.shifts?.end_time}
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">Work Week</label>
                            <p className="font-medium">{shift.work_weeks?.name}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">No shift information available</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>My Documents</CardTitle>
              </CardHeader>
              <CardContent>
                {employee.employee_documents && employee.employee_documents.length > 0 ? (
                  <div className="space-y-3">
                    {employee.employee_documents.map((doc: any) => (
                      <div key={doc.id} className="flex items-center justify-between border rounded p-3">
                        <div>
                          <p className="font-medium">{doc.document_type}</p>
                          <p className="text-sm text-gray-600">
                            Uploaded: {format(new Date(doc.uploaded_at), 'PPP')}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={doc.is_verified ? 'default' : 'secondary'}>
                            {doc.is_verified ? 'Verified' : 'Pending'}
                          </Badge>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(doc.file_url, '_blank')}
                          >
                            View
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">No documents found</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  )
}