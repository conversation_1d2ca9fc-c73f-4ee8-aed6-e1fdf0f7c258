import { Clock, Mail } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useNavigate } from "react-router-dom"

export default function WaitingApproval() {
  const navigate = useNavigate()

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 to-orange-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl text-center">
        <CardHeader>
          <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
          <CardTitle className="text-2xl">Waiting for HR Approval</CardTitle>
          <p className="text-gray-600">Your onboarding documents have been submitted</p>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="bg-yellow-50 p-6 rounded-lg">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Mail className="h-5 w-5 text-yellow-600" />
              <h3 className="font-medium">What happens next?</h3>
            </div>
            <div className="text-sm text-gray-700 space-y-2">
              <p>• HR team will review your contract acceptance</p>
              <p>• Your uploaded documents will be verified</p>
              <p>• You'll receive an email notification once approved</p>
              <p>• After approval, you can access the full system</p>
            </div>
          </div>
          
          <div className="text-sm text-gray-600">
            <p>This process typically takes 1-2 business days.</p>
            <p>If you have any questions, please contact HR.</p>
          </div>
          
          <Button
            variant="outline"
            onClick={() => navigate('/login')}
            className="mt-6"
          >
            Back to Login
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}