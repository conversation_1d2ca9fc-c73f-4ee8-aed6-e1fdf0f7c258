
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

const activities = [
  {
    id: 1,
    user: "<PERSON>",
    action: "Completed onboarding",
    department: "Engineering",
    time: "2 hours ago",
    avatar: "/placeholder.svg",
    type: "success"
  },
  {
    id: 2,
    user: "<PERSON>",
    action: "Submitted leave request",
    department: "Marketing",
    time: "4 hours ago",
    avatar: "/placeholder.svg",
    type: "pending"
  },
  {
    id: 3,
    user: "<PERSON>",
    action: "Performance review due",
    department: "Sales",
    time: "1 day ago",
    avatar: "/placeholder.svg",
    type: "warning"
  },
  {
    id: 4,
    user: "<PERSON>",
    action: "Training completed",
    department: "HR",
    time: "2 days ago",
    avatar: "/placeholder.svg",
    type: "success"
  },
  {
    id: 5,
    user: "<PERSON>",
    action: "Started new position",
    department: "Finance",
    time: "3 days ago",
    avatar: "/placeholder.svg",
    type: "info"
  }
]

const getBadgeVariant = (type: string) => {
  switch (type) {
    case 'success': return 'default'
    case 'warning': return 'destructive'
    case 'pending': return 'secondary'
    case 'info': return 'outline'
    default: return 'default'
  }
}

export function RecentActivity() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-muted/50 transition-colors">
              <Avatar className="h-10 w-10">
                <AvatarImage src={activity.avatar} alt={activity.user} />
                <AvatarFallback>{activity.user.split(' ').map(n => n[0]).join('')}</AvatarFallback>
              </Avatar>
              <div className="flex-1 space-y-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">{activity.user}</p>
                  <span className="text-xs text-muted-foreground">{activity.time}</span>
                </div>
                <p className="text-sm text-muted-foreground">{activity.action}</p>
                <Badge variant={getBadgeVariant(activity.type)} className="text-xs">
                  {activity.department}
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
