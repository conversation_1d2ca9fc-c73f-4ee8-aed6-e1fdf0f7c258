import { useState, useEffect } from "react"
import { Users, FileText, Calendar, TrendingUp, Clock, AlertCircle } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { useNavigate } from "react-router-dom"
import { supabase } from "@/integrations/supabase/client"

export default function HRDashboard() {
  const navigate = useNavigate()
  const [stats, setStats] = useState({
    totalEmployees: 0,
    activeEmployees: 0,
    pendingOnboarding: 0,
    totalContracts: 0,
    expiringSoon: 0,
    pendingApprovals: 0
  })
  const [recentEmployees, setRecentEmployees] = useState<any[]>([])
  const [pendingApprovals, setPendingApprovals] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      // Get employee stats
      const { data: employees } = await supabase
        .from('employees')
        .select('id, employment_status, onboarding_status, created_at, user_profiles(first_name, last_name)')
        .eq('is_deleted', false)

      // Get contract stats
      const { data: contracts } = await supabase
        .from('contracts')
        .select('id, end_date, status')
        .eq('is_deleted', false)

      // Get pending approvals
      const { data: approvals } = await supabase
        .from('employees')
        .select(`
          id,
          employee_code,
          onboarding_status,
          created_at,
          user_profiles(first_name, last_name)
        `)
        .eq('onboarding_status', 'docs_uploaded')
        .eq('is_deleted', false)
        .order('created_at', { ascending: false })

      // Calculate stats
      const totalEmployees = employees?.length || 0
      const activeEmployees = employees?.filter(e => e.employment_status === 'active').length || 0
      const pendingOnboarding = employees?.filter(e => e.onboarding_status !== 'approved').length || 0
      const totalContracts = contracts?.length || 0
      
      // Contracts expiring in next 30 days
      const thirtyDaysFromNow = new Date()
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)
      const expiringSoon = contracts?.filter(c => 
        c.end_date && new Date(c.end_date) <= thirtyDaysFromNow && c.status === 'active'
      ).length || 0

      setStats({
        totalEmployees,
        activeEmployees,
        pendingOnboarding,
        totalContracts,
        expiringSoon,
        pendingApprovals: approvals?.length || 0
      })

      // Recent employees (last 5)
      const recent = employees
        ?.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 5) || []
      
      setRecentEmployees(recent)
      setPendingApprovals(approvals?.slice(0, 5) || [])

    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading dashboard...</div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold">HR Dashboard</h1>
          <p className="text-gray-600">Overview of employee management and operations</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Employees</p>
                  <p className="text-3xl font-bold">{stats.totalEmployees}</p>
                  <p className="text-sm text-green-600">{stats.activeEmployees} active</p>
                </div>
                <Users className="h-12 w-12 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Onboarding</p>
                  <p className="text-3xl font-bold">{stats.pendingOnboarding}</p>
                  <p className="text-sm text-orange-600">Requires attention</p>
                </div>
                <Clock className="h-12 w-12 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Contracts</p>
                  <p className="text-3xl font-bold">{stats.totalContracts}</p>
                  <p className="text-sm text-red-600">{stats.expiringSoon} expiring soon</p>
                </div>
                <FileText className="h-12 w-12 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Pending Approvals */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-orange-600" />
                Pending Approvals ({stats.pendingApprovals})
              </CardTitle>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => navigate('/hr/approvals')}
              >
                View All
              </Button>
            </CardHeader>
            <CardContent>
              {pendingApprovals.length > 0 ? (
                <div className="space-y-3">
                  {pendingApprovals.map((employee) => (
                    <div key={employee.id} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                      <div>
                        <p className="font-medium">
                          {employee.user_profiles.first_name} {employee.user_profiles.last_name}
                        </p>
                        <p className="text-sm text-gray-600">{employee.employee_code}</p>
                      </div>
                      <Badge variant="secondary">
                        {employee.onboarding_status}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-gray-500 py-4">No pending approvals</p>
              )}
            </CardContent>
          </Card>

          {/* Recent Employees */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                Recent Employees
              </CardTitle>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => navigate('/employees')}
              >
                View All
              </Button>
            </CardHeader>
            <CardContent>
              {recentEmployees.length > 0 ? (
                <div className="space-y-3">
                  {recentEmployees.map((employee) => (
                    <div key={employee.id} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div>
                        <p className="font-medium">
                          {employee.user_profiles.first_name} {employee.user_profiles.last_name}
                        </p>
                        <p className="text-sm text-gray-600">
                          Added {new Date(employee.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge variant={employee.employment_status === 'active' ? 'default' : 'secondary'}>
                        {employee.employment_status}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-gray-500 py-4">No recent employees</p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button 
                variant="outline" 
                className="h-20 flex-col gap-2"
                onClick={() => navigate('/employees/add')}
              >
                <Users className="h-6 w-6" />
                Add Employee
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex-col gap-2"
                onClick={() => navigate('/contracts/add')}
              >
                <FileText className="h-6 w-6" />
                Create Contract
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex-col gap-2"
                onClick={() => navigate('/hr/approvals')}
              >
                <AlertCircle className="h-6 w-6" />
                Review Approvals
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex-col gap-2"
                onClick={() => navigate('/employees')}
              >
                <TrendingUp className="h-6 w-6" />
                View Reports
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  )
}