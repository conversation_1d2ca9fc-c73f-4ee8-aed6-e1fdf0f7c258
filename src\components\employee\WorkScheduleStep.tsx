
import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { supabase } from "@/integrations/supabase/client"

interface WorkScheduleData {
  shift_id: string
  work_week_id: string
}

interface WorkScheduleStepProps {
  data: WorkScheduleData
  onChange: (data: Partial<WorkScheduleData>) => void
}

interface Shift {
  id: string
  name: string
  start_time: string
  end_time: string
  break_duration: number
  grace_period: number
}

interface WorkWeek {
  id: string
  name: string
  monday: boolean
  tuesday: boolean
  wednesday: boolean
  thursday: boolean
  friday: boolean
  saturday: boolean
  sunday: boolean
}

export default function WorkScheduleStep({ data, onChange }: WorkScheduleStepProps) {
  const [shifts, setShifts] = useState<Shift[]>([])
  const [workWeeks, setWorkWeeks] = useState<WorkWeek[]>([])
  const [selectedShift, setSelectedShift] = useState<Shift | null>(null)
  const [selectedWorkWeek, setSelectedWorkWeek] = useState<WorkWeek | null>(null)

  useEffect(() => {
    fetchShifts()
    fetchWorkWeeks()
  }, [])

  useEffect(() => {
    if (data.shift_id) {
      const shift = shifts.find(s => s.id === data.shift_id)
      setSelectedShift(shift || null)
    }
  }, [data.shift_id, shifts])

  useEffect(() => {
    if (data.work_week_id) {
      const workWeek = workWeeks.find(w => w.id === data.work_week_id)
      setSelectedWorkWeek(workWeek || null)
    }
  }, [data.work_week_id, workWeeks])

  const fetchShifts = async () => {
    try {
      const { data: shiftsData, error } = await supabase
        .from('shifts')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('name')

      if (error) throw error
      setShifts(shiftsData || [])
    } catch (error) {
      console.error('Error fetching shifts:', error)
    }
  }

  const fetchWorkWeeks = async () => {
    try {
      const { data: workWeeksData, error } = await supabase
        .from('work_weeks')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('name')

      if (error) throw error
      setWorkWeeks(workWeeksData || [])
    } catch (error) {
      console.error('Error fetching work weeks:', error)
    }
  }

  const handleInputChange = (field: keyof WorkScheduleData, value: any) => {
    onChange({ [field]: value })
  }

  const formatTime = (time: string) => {
    return new Date(`1970-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const getWorkingDays = (workWeek: WorkWeek) => {
    const days = []
    if (workWeek.monday) days.push('Mon')
    if (workWeek.tuesday) days.push('Tue')
    if (workWeek.wednesday) days.push('Wed')
    if (workWeek.thursday) days.push('Thu')
    if (workWeek.friday) days.push('Fri')
    if (workWeek.saturday) days.push('Sat')
    if (workWeek.sunday) days.push('Sun')
    return days.join(', ')
  }

  return (
    <div className="space-y-6">
      {/* Shift Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Work Shift</h3>

        <div>
          <Label htmlFor="shift" className="text-sm font-medium">
            Select Shift <span className="text-red-500">*</span>
          </Label>
          <Select 
            value={data.shift_id} 
            onValueChange={(value) => handleInputChange('shift_id', value)}
          >
            <SelectTrigger className="mt-1.5">
              <SelectValue placeholder="Select work shift" />
            </SelectTrigger>
            <SelectContent>
              {shifts.map((shift) => (
                <SelectItem key={shift.id} value={shift.id}>
                  {shift.name} ({formatTime(shift.start_time)} - {formatTime(shift.end_time)})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {selectedShift && (
          <div className="bg-muted/30 rounded-lg p-4">
            <h4 className="font-medium mb-2">Shift Details</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium">Start:</span>
                <p>{formatTime(selectedShift.start_time)}</p>
              </div>
              <div>
                <span className="font-medium">End:</span>
                <p>{formatTime(selectedShift.end_time)}</p>
              </div>
              <div>
                <span className="font-medium">Break:</span>
                <p>{selectedShift.break_duration} min</p>
              </div>
              <div>
                <span className="font-medium">Grace:</span>
                <p>{selectedShift.grace_period} min</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Work Week Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Work Week Pattern</h3>

        <div>
          <Label htmlFor="work_week" className="text-sm font-medium">
            Select Work Week <span className="text-red-500">*</span>
          </Label>
          <Select 
            value={data.work_week_id} 
            onValueChange={(value) => handleInputChange('work_week_id', value)}
          >
            <SelectTrigger className="mt-1.5">
              <SelectValue placeholder="Select work week pattern" />
            </SelectTrigger>
            <SelectContent>
              {workWeeks.map((workWeek) => (
                <SelectItem key={workWeek.id} value={workWeek.id}>
                  {workWeek.name} ({getWorkingDays(workWeek)})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {selectedWorkWeek && (
          <div className="bg-muted/30 rounded-lg p-4">
            <h4 className="font-medium mb-3">Work Week Pattern</h4>
            <div className="grid grid-cols-7 gap-2">
              {[
                { key: 'monday', label: 'Mon' },
                { key: 'tuesday', label: 'Tue' },
                { key: 'wednesday', label: 'Wed' },
                { key: 'thursday', label: 'Thu' },
                { key: 'friday', label: 'Fri' },
                { key: 'saturday', label: 'Sat' },
                { key: 'sunday', label: 'Sun' }
              ].map((day) => (
                <div
                  key={day.key}
                  className={`text-center p-2 rounded text-xs font-medium ${
                    selectedWorkWeek[day.key as keyof WorkWeek]
                      ? 'bg-primary/10 text-primary border border-primary/20'
                      : 'bg-gray-100 text-gray-500 border border-gray-200'
                  }`}
                >
                  {day.label}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}