import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom"
import { ArrowLeft, Plus, Trash2 } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"

interface RequiredDocument {
  id?: string
  document_type: string
  is_mandatory: boolean
  remarks: string
}

interface ContractTemplate {
  id?: string
  name: string
  content: string
  variables: string
}

export default function EditContractType() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    description: "",
    is_active: true,
  })

  const [requiredDocuments, setRequiredDocuments] = useState<RequiredDocument[]>([])
  const [contractTemplates, setContractTemplates] = useState<ContractTemplate[]>([])

  useEffect(() => {
    if (id) {
      fetchContractType()
    }
  }, [id])

  const fetchContractType = async () => {
    try {
      // Fetch contract type
      const { data: contractData, error: contractError } = await supabase
        .from('contract_types')
        .select('*')
        .eq('id', id)
        .single()

      if (contractError) throw contractError

      setFormData({
        name: contractData.name,
        code: contractData.code,
        description: contractData.description,
        is_active: contractData.is_active,
      })

      // Fetch required documents
      const { data: documents, error: documentsError } = await supabase
        .from('contract_type_required_documents')
        .select('*')
        .eq('contract_type_id', id)
        .eq('is_active', true)

      if (documentsError) throw documentsError
      setRequiredDocuments(documents || [])

      // Fetch templates
      const { data: templates, error: templatesError } = await supabase
        .from('contract_templates')
        .select('*')
        .eq('contract_type_id', id)
        .eq('is_active', true)

      if (templatesError) throw templatesError
      setContractTemplates(templates?.map(t => ({
        ...t,
        variables: t.variables ? JSON.stringify(t.variables) : ""
      })) || [])

    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch contract type details",
        variant: "destructive",
      })
      navigate('/masters/contract-types')
    } finally {
      setLoading(false)
    }
  }

  const addRequiredDocument = () => {
    setRequiredDocuments([...requiredDocuments, { document_type: "", is_mandatory: true, remarks: "" }])
  }

  const removeRequiredDocument = (index: number) => {
    setRequiredDocuments(requiredDocuments.filter((_, i) => i !== index))
  }

  const updateRequiredDocument = (index: number, field: keyof RequiredDocument, value: string | boolean) => {
    const updated = [...requiredDocuments]
    updated[index] = { ...updated[index], [field]: value }
    setRequiredDocuments(updated)
  }

  const addContractTemplate = () => {
    setContractTemplates([...contractTemplates, { name: "", content: "", variables: "" }])
  }

  const removeContractTemplate = (index: number) => {
    setContractTemplates(contractTemplates.filter((_, i) => i !== index))
  }

  const updateContractTemplate = (index: number, field: keyof ContractTemplate, value: string) => {
    const updated = [...contractTemplates]
    updated[index] = { ...updated[index], [field]: value }
    setContractTemplates(updated)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      // Update contract type
      const { error: contractError } = await supabase
        .from('contract_types')
        .update({
          name: formData.name,
          code: formData.code,
          description: formData.description,
          is_active: formData.is_active,
        })
        .eq('id', id)

      if (contractError) throw contractError

      // Delete existing documents and templates
      await supabase.from('contract_type_required_documents').update({ is_deleted: true }).eq('contract_type_id', id)
      await supabase.from('contract_templates').update({ is_deleted: true }).eq('contract_type_id', id)

      // Insert updated documents
      const validDocuments = requiredDocuments.filter(doc => doc.document_type.trim())
      if (validDocuments.length > 0) {
        const documentsToInsert = validDocuments.map(doc => ({
          contract_type_id: id,
          document_type: doc.document_type,
          is_mandatory: doc.is_mandatory,
          remarks: doc.remarks || null,
        }))

        const { error: documentsError } = await supabase
          .from('contract_type_required_documents')
          .insert(documentsToInsert)

        if (documentsError) throw documentsError
      }

      // Insert updated templates
      const validTemplates = contractTemplates.filter(template => template.name.trim() && template.content.trim())
      if (validTemplates.length > 0) {
        const templatesToInsert = validTemplates.map(template => {
          let variables = null
          if (template.variables && template.variables.trim()) {
            try {
              variables = JSON.parse(template.variables)
            } catch (e) {
              console.warn('Invalid JSON in variables field:', e)
            }
          }
          return {
            contract_type_id: id,
            name: template.name,
            content: template.content,
            variables: variables,
          }
        })

        const { error: templatesError } = await supabase
          .from('contract_templates')
          .insert(templatesToInsert)

        if (templatesError) throw templatesError
      }

      toast({
        title: "Success",
        description: "Contract type updated successfully",
      })

      navigate('/masters/contract-types')
    } catch (error: any) {
      console.error('Error updating contract type:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to update contract type",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading...</div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => navigate('/masters/contract-types')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Contract Types
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Edit Contract Type</h1>
            <p className="text-muted-foreground">Update contract type details</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Contract Type Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="code">Code *</Label>
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value.toUpperCase() }))}
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  required
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                />
                <Label htmlFor="is_active">Active</Label>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Required Documents</CardTitle>
                <Button type="button" variant="outline" size="sm" onClick={addRequiredDocument}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Document
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {requiredDocuments.map((doc, index) => (
                <div key={index} className="space-y-4 p-4 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <Input
                      placeholder="Document type"
                      value={doc.document_type}
                      onChange={(e) => updateRequiredDocument(index, 'document_type', e.target.value)}
                      className="flex-1 mr-4"
                    />
                    <div className="flex items-center space-x-2 mr-4">
                      <Switch
                        checked={doc.is_mandatory}
                        onCheckedChange={(checked) => updateRequiredDocument(index, 'is_mandatory', checked)}
                      />
                      <Label className="text-sm">Mandatory</Label>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeRequiredDocument(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  <Input
                    placeholder="Remarks (optional)"
                    value={doc.remarks}
                    onChange={(e) => updateRequiredDocument(index, 'remarks', e.target.value)}
                  />
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Contract Templates</CardTitle>
                <Button type="button" variant="outline" size="sm" onClick={addContractTemplate}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Template
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {contractTemplates.map((template, index) => (
                <div key={index} className="space-y-4 p-4 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <Input
                      placeholder="Template name"
                      value={template.name}
                      onChange={(e) => updateContractTemplate(index, 'name', e.target.value)}
                      className="flex-1 mr-4"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeContractTemplate(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  <Textarea
                    placeholder="Template content"
                    value={template.content}
                    onChange={(e) => updateContractTemplate(index, 'content', e.target.value)}
                    rows={6}
                  />
                  <Textarea
                    placeholder="Variables (JSON format, optional)"
                    value={template.variables}
                    onChange={(e) => updateContractTemplate(index, 'variables', e.target.value)}
                    rows={2}
                  />
                </div>
              ))}
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline" onClick={() => navigate('/masters/contract-types')}>
              Cancel
            </Button>
            <Button type="submit" disabled={saving}>
              {saving ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </form>
      </div>
    </Layout>
  )
}