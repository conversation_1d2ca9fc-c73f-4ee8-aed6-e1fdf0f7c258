
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface WorkDetailsData {
  employment_status: string
  onboarding_status: string
}

interface WorkDetailsStepProps {
  data: WorkDetailsData
  onChange: (data: Partial<WorkDetailsData>) => void
}

export default function WorkDetailsStep({ data, onChange }: WorkDetailsStepProps) {
  const handleInputChange = (field: keyof WorkDetailsData, value: any) => {
    onChange({ [field]: value })
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Employment Status</h3>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="employment_status">Employment Status</Label>
            <Select 
              value={data.employment_status} 
              onValueChange={(value) => handleInputChange('employment_status', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select employment status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="on_leave">On Leave</SelectItem>
                <SelectItem value="terminated">Terminated</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="onboarding_status">Onboarding Status</Label>
            <Select 
              value={data.onboarding_status} 
              onValueChange={(value) => handleInputChange('onboarding_status', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select onboarding status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="contract_accepted">Contract Accepted</SelectItem>
                <SelectItem value="docs_uploaded">Documents Uploaded</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </div>
  )
}