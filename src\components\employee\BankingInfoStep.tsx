
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"

interface BankingInfoData {
  bank_name: string
  account_holder_name: string
  account_number: string
  ifsc_code: string
  branch_name: string
  is_primary: boolean
}

interface BankingInfoStepProps {
  data: BankingInfoData
  onChange: (data: Partial<BankingInfoData>) => void
}

export default function BankingInfoStep({ data, onChange }: BankingInfoStepProps) {
  const handleInputChange = (field: keyof BankingInfoData, value: any) => {
    onChange({ [field]: value })
  }

  const validateIFSC = (ifsc: string) => {
    const ifscRegex = /^[A-Z]{4}0[A-Z0-9]{6}$/
    return ifscRegex.test(ifsc.toUpperCase())
  }

  const handleIFSCChange = (value: string) => {
    const upperValue = value.toUpperCase()
    handleInputChange('ifsc_code', upperValue)
  }

  return (
    <div className="space-y-6">
      {/* Bank Details */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Bank Details</h3>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="bank_name">Bank Name</Label>
            <Input
              id="bank_name"
              value={data.bank_name}
              onChange={(e) => handleInputChange('bank_name', e.target.value)}
              placeholder="Enter bank name"
            />
          </div>

          <div>
            <Label htmlFor="branch_name">Branch Name</Label>
            <Input
              id="branch_name"
              value={data.branch_name}
              onChange={(e) => handleInputChange('branch_name', e.target.value)}
              placeholder="Enter branch name"
            />
          </div>
        </div>
      </div>

      {/* Account Details */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Account Details</h3>

        <div>
          <Label htmlFor="account_holder_name">Account Holder Name</Label>
          <Input
            id="account_holder_name"
            value={data.account_holder_name}
            onChange={(e) => handleInputChange('account_holder_name', e.target.value)}
            placeholder="Enter account holder name"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="account_number">Account Number</Label>
            <Input
              id="account_number"
              value={data.account_number}
              onChange={(e) => handleInputChange('account_number', e.target.value)}
              placeholder="Enter account number"
            />
          </div>

          <div>
            <Label htmlFor="ifsc_code">IFSC Code</Label>
            <Input
              id="ifsc_code"
              value={data.ifsc_code}
              onChange={(e) => handleIFSCChange(e.target.value)}
              placeholder="Enter IFSC code"
              maxLength={11}
            />
            {data.ifsc_code && !validateIFSC(data.ifsc_code) && (
              <p className="text-xs text-red-500 mt-1">Invalid IFSC code format</p>
            )}
          </div>
        </div>
      </div>

      {/* Account Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Account Settings</h3>

        <div className="flex items-center space-x-3 p-4 bg-muted/30 rounded-lg">
          <Checkbox
            id="is_primary"
            checked={data.is_primary}
            onCheckedChange={(checked) => handleInputChange('is_primary', checked)}
          />
          <div className="flex-1">
            <Label htmlFor="is_primary" className="font-medium cursor-pointer">
              Primary Account
            </Label>
            <p className="text-sm text-gray-600">
              This will be the default account for salary payments
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}