import React from "react";
import { Link } from "react-router-dom";
import GridShape from "../common/GridShape";
import ThemeToggler from "../common/ThemeToggler";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative p-6 bg-white z-1 dark:bg-gray-900 sm:p-0">
      <div className="relative flex flex-col justify-center w-full h-screen lg:flex-row dark:bg-gray-900 sm:p-0">
        {children}
        <div className="items-center hidden w-full h-full lg:w-1/2 bg-primary dark:bg-white/5 lg:grid">
          <div className="relative flex items-center justify-center z-1">
            <GridShape />
            <div className="flex flex-col items-center max-w-xs">
              <Link to="/" className="block mb-4">
                <div className="flex items-center space-x-2">
                  <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                    <span className="text-primary font-bold text-xl">ML</span>
                  </div>
                  <span className="text-white font-bold text-xl">MyLegalDesk</span>
                </div>
              </Link>
              <p className="text-center text-gray-300 dark:text-white/60">
                Professional Legal Management System
              </p>
            </div>
          </div>
        </div>
        <div className="fixed z-50 hidden bottom-6 right-6 sm:block">
          <ThemeToggler />
        </div>
      </div>
    </div>
  );
}